locals {
  resource_group_name     = var.resourceGroupName
  app_service_plan_prefix = "${var.appServicePlanAbbreviation}-${var.productName}"
  search_service_prefix   = "${var.searchServiceAbbreviation}-${var.productName}"
  sql_elastic_pool_prefix = "${var.sqlElasticPoolAbbreviation}-${var.productName}"
}

// Existing ----------------------------------------------------------------------------------------------------
data "azurerm_subscription" "subscription" {
  subscription_id = "cd48a980-2e56-4b29-9152-03e484d091ea"
}

data "azurerm_resource_group" "resource-group" {
  name = local.resource_group_name
}

// Shared ------------------------------------------------------------------------------------------------------
data "terraform_remote_state" "shared_state" {
  backend   = "azurerm"
  workspace = "dev" # This is set to dev because the shared state is deployed to the dev workspace
  config = {
    resource_group_name  = "iprox.open"
    storage_account_name = "iproxopentf"
    container_name       = "iprox-open-tfstate"
    key                  = "iprox-open-dev-shared.tfstate"
  }
}

output "remote_state" {
  value = data.terraform_remote_state.shared_state
}
// -------------------------------------------------------------------------------------------------------------

// Tenant Group ------------------------------------------------------------------------------------------------

// App Service Plan
module "app-service-plan" {
  source = "./app_service_plan"

  tenant_groups_configuration = var.tenantGroupsConfiguration

  app_service_plan_prefix = local.app_service_plan_prefix
  resource_group_name     = data.azurerm_resource_group.resource-group.name
  location                = var.location
  os_type                 = var.appServicePlanOsType
  tenant_group_tag_name   = var.tenantGroupTagName
}

// Search Service
module "search-service" {
  source = "./search_service"

  search_service_groups_configuration = var.searchServiceGroupsConfiguration

  search_service_prefix = local.search_service_prefix
  resource_group_name   = data.azurerm_resource_group.resource-group.name
  location              = var.location
}

// Sql Elastic Pool
module "sql-elastic-pool" {
  source = "./sql_elastic_pool"

  sql_elastic_pool_groups_configuration = var.sqlElasticPoolGroupsConfiguration

  sql_elastic_pool_prefix = local.sql_elastic_pool_prefix
  resource_group_name     = data.azurerm_resource_group.resource-group.name
  location                = var.location
  sql_server_name         = data.terraform_remote_state.shared_state.outputs.sql_server.fully_qualified_domain_name
}

// Frontdoor Endpoint
# module "frontdoor-endpoint" {
#   source = "./frontdoor_endpoint"

#   frontdoor_endpoint_configuration = local.frontdoor_endpoint_configuration
#   frontdoor_profile_id             = data.terraform_remote_state.shared_state.outputs.frontdoor.id
#   frontdoor_endpoint_prefix        = var.frontDoorEndpointPrefix
# }

// -------------------------------------------------------------------------------------------------------------
