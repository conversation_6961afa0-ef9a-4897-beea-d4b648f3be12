locals {
  resource_group_name                     = var.resourceGroupName
  sql_server_prefix                       = "${var.sqlServerAbbreviation}-${var.productName}"
  sql_server_administrator_login_username = local.sql_server_prefix
  storage_account_name                    = "${var.storageAccountAbbreviation}${var.storageAccountName}"
  log_analytics_workspace_prefix          = "${var.logAnalyticsWorkspaceAbbreviation}-${var.productName}"
}

// Existing ----------------------------------------------------------------------------------------------------

data "azurerm_subscription" "subscription" {
  subscription_id = "cd48a980-2e56-4b29-9152-03e484d091ea"
}

data "azurerm_resource_group" "resource-group" {
  name = local.resource_group_name
}

// Shared ------------------------------------------------------------------------------------------------------

// Sql Server
module "sql-server" {
  source = "./sql_server"

  name                         = local.sql_server_prefix
  resource_group_name          = data.azurerm_resource_group.resource-group.name
  location                     = data.azurerm_resource_group.resource-group.location
  sql_server_version           = var.sqlServerVersion
  administrator_login_username = local.sql_server_administrator_login_username
}

// Log Analytics Workspace
module "log-analytics-workspace" {
  source = "./log_analytics_workspace"

  name                = local.log_analytics_workspace_prefix
  location            = var.location
  resource_group_name = data.azurerm_resource_group.resource-group.name
  sku                 = var.logAnalyticsWorkspaceSku
  retention_in_days   = var.logAnalyticsWorkspacRetentionInDays
  daily_quota_gb      = var.logAnalyticsWorkspaceDailyQuotaDb
}

// Storage Account
module "storage-account" {
  source = "./storage_account"

  name                     = local.storage_account_name
  resource_group_name      = data.azurerm_resource_group.resource-group.name
  location                 = var.location
  account_tier             = "Standard"
  account_replication_type = "LRS"

  product_tag_name = var.productTagName
  env_tag_name     = var.envTagName
  product_name     = var.productName
  env              = var.env
}

// Storage Queue
module "storage-queue" {
  source = "./storage_queue"

  name                 = var.azureQueueStorageName
  storage_account_name = module.storage-account.name
}

// -------------------------------------------------------------------------------------------------------------
