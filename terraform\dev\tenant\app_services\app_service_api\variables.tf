variable "tenants_configuration" {
  type = list(object(
    {
      name           = string
      deploymentRing = number
      shortname      = string
      plan           = string
      search         = string
      elasticpool    = string
      azureAd = list(object({
        id          = string
        name        = string
        instance    = string
        tenantId    = string
        clientId    = string
        pmsClientId = string
      }))
      graphApi = optional(object({
        tenantId = string
        clientId = string
      }))
      apiCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      pmsCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      portalCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      frontdoorEndpoint = string
      apiIpAddresses    = list(string)
      pmsIpAddresses    = list(string)
      portalIpAddresses = list(string)
      corsUrls          = list(string)
  }))
  description = "this determines the names for all resources"
}

variable "tenant_app_service_plans" {
  description = "Map of App Service Plans to their tenants"
  type = map(object(
    {
      id = string
    }
  ))
}

variable "tenant_graph_api_secrets" {
  description = "Map of Tenant Graph API Secrets to their tenants"
  type = map(object(
    {
      secret = string
    }
  ))
}

variable "tenant_search_services" {
  description = "Map of Search Services to their tenants"
  type = map(object(
    {
      id                = string
      name              = string
      primary_admin_key = string
    }
  ))
}

variable "tenant_application_insights_apis" {
  description = "Map of API Application Insights to their tenants"
  type = map(object(
    {
      connection_string = string
    }
  ))
}

variable "tenant_storage_containers" {
  description = "Map of Storage Containers to their tenants"
  type = map(object(
    {
      name = string
    }
  ))
}

variable "tenant_sql_database_apis" {
  description = "Map of SQL Databases to their tenants"
  type = map(object(
    {
      name = string
    }
  ))
}

variable "tenant_key_vaults" {
  description = "Map of Key Vaults to their tenants"
  type = map(object(
    {
      id        = string
      vault_uri = string
    }
  ))
}

variable "storage_account_name" {
  type = string
}

variable "storage_account_access_key" {
  type = string
}

variable "storage_queue_name" {
  type = string
}

variable "storage_account_sas_token" {
  type = string
}

variable "sql_server_name" {
  type = string
}

variable "sql_server_admin_username" {
  type = string
}

variable "sql_server_admin_password" {
  type = string
}

variable "app_service_api_prefix" {
  type = string
}

variable "app_service_portal_prefix" {
  type = string
}

variable "resource_group_name" {
  type = string
}

variable "location" {
  type = string
}

variable "https_only" {
  type = string
}

variable "api_dotnet_version" {
  type = string
}

variable "cognitive_search_index_prefix" {
  type = string
}

variable "api_app_settings" {
  type = map(string)
}

variable "sql_connection_string_name" {
  type = string
}

variable "sql_connection_string_type" {
  type = string
}

variable "tenant_alias_tag_name" {
  type = string
}

variable "tenant_shortname_tag_name" {
  type = string
}

variable "product_tag_name" {
  type = string
}

variable "env_tag_name" {
  type = string
}

variable "product_name" {
  type = string
}

variable "env" {
  type = string
}

variable "download_settings" {
  description = "Download profile settings"
  type = object({
    timeLimit = string,
    profiles = list(object({
      name = string,
      speedLimits = list(object({
        threshold = string,
        speed     = string
      }))
    })),
    unlimitedDownloadIps  = list(string),
    disallowedDownloadIps = list(string),
  })
}

variable "serilog_write_to" {
  description = "Serilog write to"
  type = list(object(
    {
      Name = string
      Args = optional(object(
        {
          TelemetryConverter       = optional(string)
          WebHookUrl               = optional(string)
          RestrictedToMinimumLevel = optional(string)
        }
      ))
    },
  ))
}

// Download token
variable "download_token_issuer" {
  type = string
}

variable "download_token_audience" {
  type = string
}

variable "download_token_secret_key" {
  type = string
}
