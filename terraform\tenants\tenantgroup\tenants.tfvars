tenantGroupsConfiguration = [
  {
    name = "one",
    sku  = "P1mv3"
  },
  {
    name = "02",
    sku  = "P1mv3"
  }
]

searchServiceGroupsConfiguration = [
  {
    name = "01"
    sku  = "standard"
  }
]

frontdoorEndpointConfiguration = [
  {
    name = "one"
  }
]

sqlElasticPoolGroupsConfiguration = [
  {
    name     = "pool01"
    sku      = "StandardPool"
    capacity = "50"
    tier     = "Standard"
  }
]

# beheer-{omgeving}.iprox-open.nl > PMS
# api-{omgeving}.iprox-open.nl > API
# open.{omgeving}.nl > Portal (clients can stray from this format, but we'll recommend it)



productName       = "iprox-open"
resourceGroupName = "iprox-open-tenants"

appServicePlanAbbreviation = "asp"
searchServiceAbbreviation  = "srch"
sqlElasticPoolAbbreviation = "sqlep"

appServicePlanOsType = "Linux"

# Environment
location = "westeurope" # Location for all resources
tenantId = "1036265b-2bd7-4019-a418-1b3651df7347"
