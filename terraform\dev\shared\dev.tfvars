productName       = "iprox-open"
resourceGroupName = "iprox.open"

sqlServerAbbreviation             = "sql"
logAnalyticsWorkspaceAbbreviation = "log"
frontdoorAbbreviation             = "afd"
storageAccountAbbreviation        = "st"

location = "westeurope" # Location for all resources

sqlServerVersion = "12.0"

storageAccountName = "iproxopendev"

logAnalyticsWorkspaceSku            = "PerGB2018"
logAnalyticsWorkspaceDailyQuotaDb   = "0.023" # 0.023 is minimum possible, can pass it as a string
logAnalyticsWorkspacRetentionInDays = 120

azureQueueStorageName = "iproxopendevqueue"
