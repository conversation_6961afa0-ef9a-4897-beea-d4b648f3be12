variable "name" {
  type = string
}

variable "resource_group_name" {
  type = string
}

variable "location" {
  type = string
}

variable "account_tier" {
  type = string
}

variable "account_replication_type" {
  type = string
}

variable "product_tag_name" {
  type = string
}

variable "env_tag_name" {
  type = string
}

variable "product_name" {
  type = string
}

variable "env" {
  type = string
}
