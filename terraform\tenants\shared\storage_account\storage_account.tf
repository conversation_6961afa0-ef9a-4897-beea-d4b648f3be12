resource "azurerm_storage_account" "storage-account" {
  name                     = var.name
  resource_group_name      = var.resource_group_name
  location                 = var.location
  account_tier             = var.account_tier
  account_replication_type = var.account_replication_type

  blob_properties {
    delete_retention_policy {
      days = 7
    }
  }

  tags = {
    "${var.product_tag_name}" = "${var.product_name}"
    "${var.env_tag_name}"     = "${var.env}"
  }

  lifecycle {
    prevent_destroy = true
  }
}