variable "productName" {
  type    = string
  default = "iprox-open"
}

variable "resourceGroupName" {
  type    = string
  default = "dev"
}

variable "env" {
  type    = string
  default = "production"
}

# Abbreviations
variable "sqlServerAbbreviation" {
  type    = string
  default = "sql"
}

variable "logAnalyticsWorkspaceAbbreviation" {
  type    = string
  default = "log"
}

variable "frontdoorAbbreviation" {
  type    = string
  default = "afd"
}

variable "storageAccountAbbreviation" {
  type    = string
  default = "st"
}

# Environment

variable "location" {
  type    = string
  default = "westeurope"
}

# Sql
variable "sqlServerVersion" {
  type    = string
  default = "12.0"
}

#Frontdoor
variable "frontDoorSkuName" {
  type        = string
  description = "The SKU for the Front Door profile. Possible values include: Standard_AzureFrontDoor, Premium_AzureFrontDoor"
  default     = "Standard_AzureFrontDoor"
  validation {
    condition     = contains(["Standard_AzureFrontDoor", "Premium_AzureFrontDoor"], var.frontDoorSkuName)
    error_message = "The SKU value must be one of the following: Standard_AzureFrontDoor, Premium_AzureFrontDoor."
  }
}

# Storage
variable "storageAccountName" {
  type    = string
  default = "iproxopenstorageaccount"
}

# Application Insights
variable "logAnalyticsWorkspaceSku" {
  type    = string
  default = "PerGB2018"
}

variable "logAnalyticsWorkspaceDailyQuotaDb" {
  type    = string
  default = "0.023"
}

variable "azureQueueStorageName" {
  description = "Azure Queue Name"
  type        = string
}

variable "logAnalyticsWorkspacRetentionInDays" {
  type    = number
  default = 120
}

// Tags
variable "productTagName" {
  type        = string
  description = "Product tag name"
  default     = "product"
}

variable "envTagName" {
  type        = string
  description = "Environment tag name"
  default     = "env"
}