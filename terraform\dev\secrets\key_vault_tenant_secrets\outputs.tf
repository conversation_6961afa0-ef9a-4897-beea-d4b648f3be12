output "tenant_secrets_params" {
  description = "Map of AAD Client Secrets to their tenants"
  value = { for tenant_name, secret in data.azurerm_key_vault_secret.tenant-secrets :
    tenant_name => {
      secret = secret.value
    }
  }
}

output "tenant_graph_api_secrets_params" {
  description = "Map of Graph API Client Secrets to their tenants"
  value = { for tenant_name, secret in data.azurerm_key_vault_secret.tenant-graph-api-secrets :
    tenant_name => {
      secret = secret.value
    }
  }
}
