output "app_service_api_params" {
  description = "Map of App Service Parameters to their tenants"
  value = { for tenant_name, app_service_api in azurerm_linux_web_app.app-service-apis :
    tenant_name => {
      name                          = app_service_api.name
      default_hostname              = app_service_api.default_hostname
      principal_id                  = app_service_api.identity[0].principal_id
      custom_domain_verification_id = app_service_api.custom_domain_verification_id
    }
  }
}
