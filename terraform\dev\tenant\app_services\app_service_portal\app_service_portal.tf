resource "azurerm_linux_web_app" "app-service-portals" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  name                = "${var.app_service_portal_prefix}-${each.value.shortname}"
  location            = var.location
  resource_group_name = var.resource_group_name
  service_plan_id     = lookup(var.tenant_app_service_plans, "${each.value.plan}").id
  https_only          = true

  site_config {
    app_command_line = "node apps/iprox.open.portal/server.js"
    application_stack {
      node_version = var.portal_node_version
    }

    ip_restriction_default_action = length(each.value.portalIpAddresses) != 0 || each.value.portalCustomDomain.provider == "adn-clf" ? "Deny" : "Allow"

    dynamic "ip_restriction" {
      for_each = each.value.portalCustomDomain.provider == "adn-clf" ? concat(each.value.portalIpAddresses, var.default_clf_addresses) : each.value.portalIpAddresses
      content {
        ip_address = strcontains(ip_restriction.value, "/") ? ip_restriction.value : "${ip_restriction.value}/32"
        name       = ip_restriction.value
        priority   = 100
        action     = "Allow"
      }
    }
  }

  identity {
    type = "SystemAssigned"
  }

  app_settings = merge(var.portal_app_settings, { # use dynammic blocks
    "APPLICATIONINSIGHTS_CONNECTION_STRING" = lookup(var.tenant_application_insights_portals, "${each.value.name}").connection_string
    "IPROX_OPEN_API_URL"                    = each.value.apiCustomDomain.provider != "adn-awn" ? "https://${each.value.apiCustomDomain.hostName}/api/v1/public" : "https://${lookup(var.tenant_app_service_apis, "${each.value.name}").name}.azurewebsites.net/api/v1/public"
    "BASE_URL"                              = each.value.portalCustomDomain.provider != "adn-awn" ? "https://${each.value.portalCustomDomain.hostName}" : "https://app-iprox-open-portal-${each.value.shortname}.azurewebsites.net"
    "NEXT_PUBLIC_SERVICE_DESK_URL"          = "https://iprox-open-dev.service-now.com"
  })

  tags = {
    "${var.tenant_alias_tag_name}"     = "${each.value.name}"
    "${var.tenant_shortname_tag_name}" = "${each.value.shortname}"
    "${var.product_tag_name}"          = "${var.product_name}"
    "${var.env_tag_name}"              = "${var.env}"
  }
}
