# Ensure that our deployment Service Principal is added to the Access Policies
resource "azurerm_key_vault_access_policy" "key-vault-access-policy-agent" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  key_vault_id = lookup(var.tenant_key_vaults, "${each.value.name}").id

  tenant_id = var.tenant_id
  object_id = "d31ac3c5-3628-4f14-95ae-91e09356d459" # data.azurerm_client_config.current.object_id

  lifecycle {
    create_before_destroy = true
  }

  key_permissions         = var.key_vault_key_permissions_full
  secret_permissions      = var.key_vault_secret_permissions_full
  certificate_permissions = var.key_vault_certificate_permissions_full
  storage_permissions     = var.key_vault_storage_permissions_full
}

resource "azurerm_key_vault_access_policy" "key-vault-access-policy-api" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  key_vault_id = lookup(var.tenant_key_vaults, "${each.value.name}").id

  tenant_id = var.tenant_id
  object_id = lookup(var.tenant_app_service_apis, "${each.value.name}").principal_id

  key_permissions = [
    "Get",
    "List",
  ]

  secret_permissions = [
    # "Set",
    "Get",
    "List",
  ]
}

resource "azurerm_key_vault_access_policy" "key-vault-access-policy-pms" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  key_vault_id = lookup(var.tenant_key_vaults, "${each.value.name}").id

  tenant_id = var.tenant_id
  object_id = lookup(var.tenant_app_service_pmss, "${each.value.name}").principal_id

  key_permissions = [
    "Get",
  ]

  secret_permissions = [
    "Get",
  ]
}

#2947c0d8-bc28-4ed9-90a6-fb88c3d44e58 iprox.open devops-admin
resource "azurerm_key_vault_access_policy" "key-vault-access-policy-devops-admin" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  key_vault_id = lookup(var.tenant_key_vaults, "${each.value.name}").id

  tenant_id = var.tenant_id
  object_id = "2947c0d8-bc28-4ed9-90a6-fb88c3d44e58"

  key_permissions         = var.key_vault_key_permissions_full
  secret_permissions      = var.key_vault_secret_permissions_full
  certificate_permissions = var.key_vault_certificate_permissions_full
  storage_permissions     = var.key_vault_storage_permissions_full
}
