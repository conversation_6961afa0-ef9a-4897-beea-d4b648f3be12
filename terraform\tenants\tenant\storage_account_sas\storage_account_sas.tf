data "azurerm_storage_account_sas" "storage-account-sas" {
  connection_string = var.connection_string
  https_only        = var.https_only
  signed_version    = "2022-11-02"

  resource_types {
    service   = var.resource_types_service
    container = var.resource_types_container
    object    = var.resource_types_object
  }

  services {
    blob  = var.services_blob
    queue = var.services_queue
    table = var.services_table
    file  = var.services_file
  }

  start  = var.start_date
  expiry = var.expiry_date

  permissions {
    read    = var.read_permission
    write   = var.write_permission
    delete  = var.delete_permission
    list    = var.list_permission
    add     = var.add_permission
    create  = var.create_permission
    update  = var.update_permission
    process = var.process_permission
    tag     = var.tag_permission
    filter  = var.filter_permission
  }
}