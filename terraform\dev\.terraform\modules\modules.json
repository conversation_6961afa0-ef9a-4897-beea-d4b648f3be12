{"Modules": [{"Key": "", "Source": "", "Dir": "."}, {"Key": "app-service-api", "Source": "./modules/tenant/app_services/app_service_api", "Dir": "modules/tenant/app_services/app_service_api"}, {"Key": "app-service-plan", "Source": "./modules/tenantgroup/app_service_plan", "Dir": "modules/tenantgroup/app_service_plan"}, {"Key": "app-service-pms", "Source": "./modules/tenant/app_services/app_service_pms", "Dir": "modules/tenant/app_services/app_service_pms"}, {"Key": "app-service-portal", "Source": "./modules/tenant/app_services/app_service_portal", "Dir": "modules/tenant/app_services/app_service_portal"}, {"Key": "application-insights-api", "Source": "./modules/tenant/application_insights/application_insights_api", "Dir": "modules/tenant/application_insights/application_insights_api"}, {"Key": "application-insights-pms", "Source": "./modules/tenant/application_insights/application_insights_pms", "Dir": "modules/tenant/application_insights/application_insights_pms"}, {"Key": "application-insights-portal", "Source": "./modules/tenant/application_insights/application_insights_portal", "Dir": "modules/tenant/application_insights/application_insights_portal"}, {"Key": "key-vault", "Source": "./modules/tenant/key_vault", "Dir": "modules/tenant/key_vault"}, {"Key": "key-vault-access-policy", "Source": "./modules/tenant/key_vault_access_policy", "Dir": "modules/tenant/key_vault_access_policy"}, {"Key": "key-vault-secret", "Source": "./modules/tenant/key_vault_secret", "Dir": "modules/tenant/key_vault_secret"}, {"Key": "log-analytics-workspace", "Source": "./modules/shared/log_analytics_workspace", "Dir": "modules/shared/log_analytics_workspace"}, {"Key": "search-service", "Source": "./modules/tenantgroup/search_service", "Dir": "modules/tenantgroup/search_service"}, {"Key": "sql-database-api", "Source": "./modules/tenant/sql_databases/sql_database_api", "Dir": "modules/tenant/sql_databases/sql_database_api"}, {"Key": "sql-database-pms-auth", "Source": "./modules/tenant/sql_databases/sql_database_pms_auth", "Dir": "modules/tenant/sql_databases/sql_database_pms_auth"}, {"Key": "sql-elastic-pool", "Source": "./modules/tenantgroup/sql_elastic_pool", "Dir": "modules/tenantgroup/sql_elastic_pool"}, {"Key": "sql-server", "Source": "./modules/shared/sql_server", "Dir": "modules/shared/sql_server"}, {"Key": "storage-account", "Source": "./modules/shared/storage_account", "Dir": "modules/shared/storage_account"}, {"Key": "storage-account-sas", "Source": "./modules/tenant/storage_account_sas", "Dir": "modules/tenant/storage_account_sas"}, {"Key": "storage-container", "Source": "./modules/tenant/storage_container", "Dir": "modules/tenant/storage_container"}, {"Key": "storage-container-sas", "Source": "./modules/tenant/storage_container_sas", "Dir": "modules/tenant/storage_container_sas"}, {"Key": "storage-queue", "Source": "./modules/shared/storage_queue", "Dir": "modules/shared/storage_queue"}, {"Key": "tenant_secrets", "Source": "./modules/shared/key_vault_tenant_secrets", "Dir": "modules/shared/key_vault_tenant_secrets"}]}