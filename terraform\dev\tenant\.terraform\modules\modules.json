{"Modules": [{"Key": "", "Source": "", "Dir": "."}, {"Key": "app-service-api", "Source": "./app_services/app_service_api", "Dir": "app_services/app_service_api"}, {"Key": "app-service-pms", "Source": "./app_services/app_service_pms", "Dir": "app_services/app_service_pms"}, {"Key": "app-service-portal", "Source": "./app_services/app_service_portal", "Dir": "app_services/app_service_portal"}, {"Key": "application-insights-api", "Source": "./application_insights/application_insights_api", "Dir": "application_insights/application_insights_api"}, {"Key": "application-insights-pms", "Source": "./application_insights/application_insights_pms", "Dir": "application_insights/application_insights_pms"}, {"Key": "application-insights-portal", "Source": "./application_insights/application_insights_portal", "Dir": "application_insights/application_insights_portal"}, {"Key": "key-vault", "Source": "./key_vault", "Dir": "key_vault"}, {"Key": "key-vault-access-policy", "Source": "./key_vault_access_policy", "Dir": "key_vault_access_policy"}, {"Key": "key-vault-secret", "Source": "./key_vault_secret", "Dir": "key_vault_secret"}, {"Key": "pr-search-services", "Source": "./pr_search_services", "Dir": "pr_search_services"}, {"Key": "sql-database-api", "Source": "./sql_databases/sql_database_api", "Dir": "sql_databases/sql_database_api"}, {"Key": "sql-database-pms-auth", "Source": "./sql_databases/sql_database_pms_auth", "Dir": "sql_databases/sql_database_pms_auth"}, {"Key": "storage-account-sas", "Source": "./storage_account_sas", "Dir": "storage_account_sas"}, {"Key": "storage-container", "Source": "./storage_container", "Dir": "storage_container"}, {"Key": "storage-container-sas", "Source": "./storage_container_sas", "Dir": "storage_container_sas"}, {"Key": "tenant_secrets", "Source": "./key_vault_tenant_secrets", "Dir": "key_vault_tenant_secrets"}]}