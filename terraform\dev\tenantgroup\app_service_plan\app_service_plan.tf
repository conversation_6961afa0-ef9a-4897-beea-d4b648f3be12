resource "azurerm_service_plan" "app-service-plans" {
  for_each = { for tenant_group in var.tenant_groups_configuration : tenant_group.name => tenant_group }

  name                = "${var.app_service_plan_prefix}-${each.value.name}"
  resource_group_name = var.resource_group_name
  location            = var.location
  os_type             = var.os_type
  sku_name            = each.value.sku
  tags = {
    "${var.tenant_group_tag_name}" = each.value.name
  }
}