variable "tenants_configuration" {
  type = list(object(
    {
      name           = string
      deploymentRing = number
      shortname      = string
      plan           = string
      search         = string
      elasticpool    = string
      azureAd = list(object({
        id          = string
        name        = string
        instance    = string
        tenantId    = string
        clientId    = string
        pmsClientId = string
      }))
      apiCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      pmsCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      portalCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      frontdoorEndpoint = string
      apiIpAddresses    = list(string)
      pmsIpAddresses    = list(string)
      portalIpAddresses = list(string)
      corsUrls          = list(string)
  }))
  description = "this determines the names for all resources"
}

variable "tenant_app_service_plans" {
  description = "Map of App Service Plans to their tenants"
  type = map(object(
    {
      id = string
    }
  ))
}

variable "tenant_application_insights_portals" {
  description = "Map of PORTAL Application Insights to their tenants"
  type = map(object(
    {
      connection_string = string
    }
  ))
}

variable "tenant_app_service_apis" {
  description = "Map of App Service API to their tenants"
  type = map(object(
    {
      name                          = string
      default_hostname              = string
      principal_id                  = string
      custom_domain_verification_id = string
    }
  ))
}

variable "app_service_portal_prefix" {
  type = string
}

variable "resource_group_name" {
  type = string
}

variable "location" {
  type = string
}

variable "https_only" {
  type = string
}

variable "portal_node_version" {
  type = string
}

variable "portal_app_settings" {
  type = map(string)
}

variable "tenant_alias_tag_name" {
  type = string
}

variable "tenant_shortname_tag_name" {
  type = string
}

variable "product_tag_name" {
  type = string
}

variable "env_tag_name" {
  type = string
}

variable "product_name" {
  type = string
}

variable "env" {
  type = string
}

variable "default_clf_addresses" {
  type    = list(string)
  default = ["************/20", "************/22", "************/22", "**********/22", "************/18", "*************/18", "************/20", "************/20", "*************/22", "************/17", "***********/15", "**********/13", "**********/14", "**********/13", "**********/22"]
}
