output "tenants_configuration" {
  value = local.tenants_configuration
}

output "tenants_to_deploy_app_services" {
  value = local.tenants_to_deploy_app_services
}

output "subscription_id" {
  value = data.azurerm_subscription.subscription.subscription_id
}

output "subscription_display_name" {
  value = data.azurerm_subscription.subscription.display_name
}

output "tenant_id" {
  value = var.tenantId
}

output "resource_group_name" {
  value = local.resource_group_name
}

output "api_sql_databases" {
  value = { for k, v in module.sql-database-api.sql_database_api_params : k => v if contains([for tenant in local.tenants_to_deploy_app_services : tenant.name], k) }
  # value = module.sql-database-api.sql_database_api_params
}

output "pms_auth_sql_databases" {
  value = { for k, v in module.sql-database-pms-auth.sql_database_pms_auth_params : k => v if contains([for tenant in local.tenants_to_deploy_app_services : tenant.name], k) }
  # value = module.sql-database-pms-auth.sql_database_pms_auth_params
}

output "api_app_services" {
  value = { for k, v in module.app-service-api.app_service_api_params : k => v if contains([for tenant in local.tenants_to_deploy_app_services : tenant.name], k) }
  # value     = module.app-service-api.app_service_api_params
  sensitive = true
}

output "pms_app_services" {
  value = { for k, v in module.app-service-pms.app_service_pms_params : k => v if contains([for tenant in local.tenants_to_deploy_app_services : tenant.name], k) }
  # value     = module.app-service-pms.app_service_pms_params
  sensitive = true
}

output "portal_app_services" {
  value = { for k, v in module.app-service-portal.app_service_portal_params : k => v if contains([for tenant in local.tenants_to_deploy_app_services : tenant.name], k) }
  # value     = module.app-service-portal.app_service_portal_params
  sensitive = true
}

output "sql_server_fully_qualified_domain_name" {
  value = data.terraform_remote_state.shared_state.outputs.sql_server.fully_qualified_domain_name
}

output "sql_server_administrator_login" {
  value = data.terraform_remote_state.shared_state.outputs.sql_server.administrator_login
}

output "sql_server_admin_username" {
  value = data.terraform_remote_state.shared_state.outputs.sql_server.admin_username
}

output "sql_server_admin_password" {
  value     = data.terraform_remote_state.shared_state.outputs.sql_server.admin_password
  sensitive = true
}
