{"version": 3, "serial": 1, "lineage": "f419d950-6bc6-9293-eece-9a92dc996a98", "backend": {"type": "azurerm", "config": {"access_key": null, "client_certificate_password": null, "client_certificate_path": null, "client_id": null, "client_secret": null, "container_name": "iprox-open-tfstate", "endpoint": null, "environment": null, "key": "iprox-open-tenants-tenant.tfstate", "metadata_host": null, "msi_endpoint": null, "oidc_request_token": null, "oidc_request_url": null, "oidc_token": null, "oidc_token_file_path": null, "resource_group_name": "iprox.open", "sas_token": null, "snapshot": null, "storage_account_name": "iproxopen", "subscription_id": null, "tenant_id": null, "use_azuread_auth": null, "use_msi": null, "use_oidc": null}, "hash": **********}, "modules": [{"path": ["root"], "outputs": {}, "resources": {}, "depends_on": []}]}