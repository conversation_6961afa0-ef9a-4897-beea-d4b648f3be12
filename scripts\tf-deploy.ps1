param(
  [Parameter(Mandatory = $true)]
  [string]$servicePrincipalId,

  [Parameter(Mandatory = $true)]
  [string]$servicePrincipalSecret,

  [Parameter(Mandatory = $true)]
  [string]$tenantId,

  [Parameter(Mandatory = $true)]
  [string]$subscriptionId,

  [Parameter(Mandatory = $true)]
  [string]$env,

  [Parameter(Mandatory = $false)]
  [string]$ringsToDeploy,

  [Parameter(Mandatory = $true)]
  [string]$target
)

# Set up environment variables
$env:ARM_CLIENT_ID = $servicePrincipalId
$env:ARM_CLIENT_SECRET = $servicePrincipalSecret
$env:ARM_TENANT_ID = $tenantId
$env:ARM_SUBSCRIPTION_ID = $subscriptionId

# Import library functions
. "$PSScriptRoot/lib.ps1"
# Import function
. "$PSScriptRoot/Invoke-TerraformDeploy.ps1"

# Set up local variables
$terraformBasePath = "$env:SYSTEM_DEFAULTWORKINGDIRECTORY/terraformSource/terraform"

if ($target -eq "tenant" -and [string]::IsNullOrEmpty($ringsToDeploy)) {
  throw "With target '$target' ringsToDeploy is mandatory"
}

# Login to Azure and set subscription
az login --service-principal -u $servicePrincipalId -p $servicePrincipalSecret -t $tenantId
az account set --subscription $subscriptionId

Invoke-TerraformDeploy -workingDirectory "$terraformBasePath/$env/$target" -workspace $env -varFile "$env.tfvars" -variables @{
  "ringsToDeploy" = $ringsToDeploy
}
