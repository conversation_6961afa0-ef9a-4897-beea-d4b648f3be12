# Import parameters
. "$PSScriptRoot/common-params.ps1"

param(
  [Parameter(Name = "Target", Mandatory = $true)]
  [string]$target
)
# Import library functions
. "$PSScriptRoot/Invoke-TerraformDeploy.ps1"

# Set up local variables
$terraformBasePath = "$env:SYSTEM_DEFAULTWORKINGDIRECTORY/terraformSource/terraform"

# Login to Azure and set subscription
az login --service-principal -u $servicePrincipalId -p $servicePrincipalSecret -t $tenantId
az account set --subscription $subscriptionId

Invoke-TerraformDeploy -workingDirectory "$terraformBasePath/$env/$target" -workspace $env -varFile "$env.tfvars" -variables @{
  "ringsToDeploy" = $ringsToDeploy
}
