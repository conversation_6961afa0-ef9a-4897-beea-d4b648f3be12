param(
  [Parameter(Mandatory = $true)]
  [string]$target
)

# Import library functions
. "$PSScriptRoot/lib.ps1"

# Import function
. "$PSScriptRoot/Invoke-TerraformDeploy.ps1"

# Set up local variables
$terraformBasePath = "$env:SYSTEM_DEFAULTWORKINGDIRECTORY/terraformSource/terraform"

# Set ringsToDeploy to null if target is not 'tenant'. Since we are using the pipeline parameters
# there is no other vector where we can set it to null.
if ($target -ne "tenant") {
  $ringsToDeploy = $null
}

# Connect to Azure
Connect-Azure -servicePrincipalId $servicePrincipalId -servicePrincipalSecret $servicePrincipalSecret -tenantId $tenantId -subscriptionId $subscriptionId

Invoke-TerraformDeploy -workingDirectory "$terraformBasePath/$environment/$target" -workspace $environment -varFile "$environment.tfvars" -variables @{
  "ringsToDeploy" = $ringsToDeploy
}
