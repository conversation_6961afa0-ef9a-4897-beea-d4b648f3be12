param(
  [Parameter(Mandatory=$true)]
  [string]$servicePrincipalId,

  [Parameter(Mandatory=$true)]
  [string]$servicePrincipalSecret,

  [Parameter(Mandatory=$true)]
  [string]$tenantId,

  [Parameter(Mandatory=$true)]
  [string]$subscriptionId,

  [Parameter(Mandatory=$true)]
  [string]$env,

  [Parameter(Mandatory=$false)]
  [string]$ringsToDeploy = "[1]"
)

# Import library functions
. "$PSScriptRoot/lib.ps1"

# Set up environement variables
$env:ARM_CLIENT_ID = $servicePrincipalId
$env:ARM_CLIENT_SECRET = $servicePrincipalSecret
$env:ARM_TENANT_ID = $tenantId
$env:ARM_SUBSCRIPTION_ID = $subscriptionId

# Set up local variables
$terraformBasePath = "$env:SYSTEM_DEFAULTWORKINGDIRECTORY/terraformSource/terraform"

# Login to Azure and set subscription
az login --service-principal -u $servicePrincipalId -p $servicePrincipalSecret -t $tenantId
az account set --subscription $subscriptionId

#-------------------------------------------------------------------------------------------------------------------------------------------------------------------
# Build Infrastructure

Write-Info 'Building general infrastructure'
Invoke-TerraformDeploy -workingDirectory "$terraformBasePath/$env/shared" -workspace $env -varFile "$env.tfvars"

Write-Info 'Building tenantgroup infrastructure'
Invoke-TerraformDeploy -workingDirectory "$terraformBasePath/$env/tenantgroup" -workspace $env -varFile "$env.tfvars"

Write-Info 'Building tenants'
Invoke-TerraformDeploy -workingDirectory "$terraformBasePath/$env/tenant" -workspace $env -varFile "$env.tfvars" -variables @{
  "ringsToDeploy" = $ringsToDeploy
}

#-------------------------------------------------------------------------------------------------------------------------------------------------------------------
# Pepare Release Pipeline Variables

Write-Info 'Using Terraform output to generate extra release info'
Push-Location "$terraformBasePath/$env/tenant"
terraform workspace select $env
$tfOutput = terraform output -json
$tfOutputObject = $tfOutput | ConvertFrom-Json
Pop-Location

$apiAppServiceOutput = 'apiAppServiceOutput'
$pmsAppServiceOutput = 'pmsAppServiceOutput'
$portalAppServiceOutput = 'portalAppServiceOutput'
$tenantIdOutput = 'tenantIdOutput'
$resourceGroupNameOutput = 'resourceGroupNameOutput'
$subscriptionIdOutput = 'subscriptionIdOutput'

Write-Info 'Fetching Release Pipeline Variables'
$releaseurl = ('{0}{1}/_apis/release/releases/{2}?api-version=5.0' -f $($env:SYSTEM_TEAMFOUNDATIONSERVERURI), $($env:SYSTEM_TEAMPROJECTID), $($env:RELEASE_RELEASEID) )
$Release = Invoke-RestMethod -Uri $releaseurl -Headers @{
  Authorization = "Bearer $($env:SYSTEM_ACCESSTOKEN)"
}

Write-Info 'Updating Release Pipeline Variables'
$Release.variables.$apiAppServiceOutput.value = $tfOutputObject.api_app_services.value | ConvertTo-Json
$Release.variables.$pmsAppServiceOutput.value = $tfOutputObject.pms_app_services.value | ConvertTo-Json
$Release.variables.$portalAppServiceOutput.value = $tfOutputObject.portal_app_services.value | ConvertTo-Json
$Release.variables.$tenantIdOutput.value = $tfOutputObject.tenant_id.value
$Release.variables.$resourceGroupNameOutput.value = $tfOutputObject.resource_group_name.value
$Release.variables.$subscriptionIdOutput.value = $tfOutputObject.subscription_id.value

Write-Info 'Saving Release Pipeline Variables'
$json = @($Release) | ConvertTo-Json -Depth 99
Invoke-RestMethod -Uri $releaseurl -Method Put -Body $json -ContentType "application/json" -Headers @{Authorization = "Bearer $($env:SYSTEM_ACCESSTOKEN)" }

# Write-Info 'Get updated Release Pipeline Variables'
# $CheckRelease = Invoke-RestMethod -Uri $releaseurl -Headers @{
#     Authorization = "Bearer $($env:SYSTEM_ACCESSTOKEN)"
# }
# Write-Host ('Release Pipeline variables output: {0}' -f $($CheckRelease.variables | ConvertTo-Json -Depth 10))

#-------------------------------------------------------------------------------------------------------------------------------------------------------------------
# API Database Migration

$adminUsername = $tfOutputObject.sql_server_admin_username.value
$adminPassword = $tfOutputObject.sql_server_admin_password.value
$serverName = "$($tfOutputObject.sql_server_fully_qualified_domain_name.value).database.windows.net"

foreach ($tenant in $tfOutputObject.api_sql_databases.value.PSObject.Properties) {
  $databaseName = $tenant.Value.name

  $cmsMigrationPath = "$env:SYSTEM_DEFAULTWORKINGDIRECTORY/iproxSource/iprox.open.api-migrations/cms/script.sql"
  Write-Host "Running CMS migration for $($tenant.Name)"

  Invoke-Sqlcmd -ServerInstance $serverName -Database $databaseName -Username $adminUsername -Password $adminPassword -InputFile $cmsMigrationPath

  $iproxMigrationPath = "$env:SYSTEM_DEFAULTWORKINGDIRECTORY/iproxSource/iprox.open.api-migrations/iprox/script.sql"
  Write-Host "Running Iprox migration for $($tenant.Name)"

  Invoke-Sqlcmd -ServerInstance $serverName -Database $databaseName -Username $adminUsername -Password $adminPassword -InputFile $iproxMigrationPath
}

# CMS Database Migration
Set-Location $env:SYSTEM_DEFAULTWORKINGDIRECTORY/iproxSource/iprox.open.pms-auth-migrations

$serverName = $tfOutputObject.sql_server_fully_qualified_domain_name.value

foreach ($tenant in $tfOutputObject.pms_auth_sql_databases.value.PSObject.Properties) {
  $databaseName = $tenant.Value.name

  $pmsDatabaseUrl = "sqlserver://$($serverName).database.windows.net:1433;database=$($databaseName);user=$($adminUsername)@$($serverName);password=$($adminPassword);encrypt=true;trustServerCertificate=false;hostNameInCertificate=*.database.windows.net;loginTimeout=30;"

  $env:PMS_DATABASE_URL = $pmsDatabaseUrl

  npx --yes prisma migrate deploy
}
