tenantGroupsConfiguration = [
  {
    name = "development",
    sku  = "P1v3"
  }
]

searchServiceGroupsConfiguration = [
  {
    name = "development"
    sku  = "basic"
  }
]

frontdoorEndpointConfiguration = [
  {
    name = "one"
  },
  {
    name = "two"
  }
]

sqlElasticPoolGroupsConfiguration = [
  {
    name     = "one"
    sku      = "StandardPool"
    capacity = "50"
    tier     = "Standard"
  }
]

productName       = "iprox-open"
resourceGroupName = "iprox.open"

appServicePlanAbbreviation = "asp"
searchServiceAbbreviation  = "srch"
sqlElasticPoolAbbreviation = "sqlep"

appServicePlanOsType = "Linux"

# Environment
location = "westeurope" # Location for all resources

# Azure
tenantId = "1036265b-2bd7-4019-a418-1b3651df7347"
