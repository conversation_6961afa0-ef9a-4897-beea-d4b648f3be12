variable "connection_string" {
  type = string
}

variable "https_only" {
  type = string
}

variable "resource_types_service" {
  type = string
}

variable "resource_types_container" {
  type = string
}

variable "resource_types_object" {
  type = string
}

variable "services_blob" {
  type = string
}

variable "services_queue" {
  type = string
}

variable "services_table" {
  type = string
}

variable "services_file" {
  type = string
}

variable "start_date" {
  type = string
}

variable "expiry_date" {
  type = string
}

variable "read_permission" {
  type = string
}

variable "write_permission" {
  type = string
}

variable "delete_permission" {
  type = string
}

variable "list_permission" {
  type = string
}

variable "add_permission" {
  type = string
}

variable "create_permission" {
  type = string
}

variable "update_permission" {
  type = string
}

variable "process_permission" {
  type = string
}

variable "tag_permission" {
  type = string
}

variable "filter_permission" {
  type = string
} 