terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.117.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.7.0"
    }
  }

  backend "azurerm" {
    resource_group_name  = "iprox.open"
    storage_account_name = "iproxopen"
    container_name       = "iprox-open-tfstate"
    key                  = "iprox-open-tenants-tenantgroup.tfstate"
  }

  required_version = ">= 1.6.0"
}
# add tags - tenant-alias, product - iprox.open for everything, tenant - production/training
# password generator

# database backups when releasing/deploying
# Deploy in groups of x number, if something goes wrong, we catch it early
# sql firewall and rule
# separate database and sql server logins

provider "azurerm" {
  features {}
}
