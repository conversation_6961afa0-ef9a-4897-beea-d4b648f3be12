Set-Location terraformSource/terraform/$env:env

az login --service-principal -u $(servicePrincipalId) -p $(servicePrincipalSecret) --tenant $(tenantId)
az account set --subscription $(subscriptionId)

terraform workspace new $(env)
terraform workspace select $(env)

terraform validate

terraform apply -auto-approve -var-file="$(env).tfvars" -var="ringsToDeploy=$(ringsToDeploy)"

$tfOutput = terraform output -json
$tfOutputObject = $tfOutput | ConvertFrom-Json

#-------------------------------------------------------------------------------------------------------------------------------------------------------------------

# Set tfOutput as a pipeline variable
$apiAppServiceOutput = 'apiAppServiceOutput'
$pmsAppServiceOutput = 'pmsAppServiceOutput'
$portalAppServiceOutput = 'portalAppServiceOutput'
$tenantIdOutput = 'tenantIdOutput'
$resourceGroupNameOutput = 'resourceGroupNameOutput'
$subscriptionIdOutput = 'subscriptionIdOutput'

$releaseurl = ('{0}{1}/_apis/release/releases/{2}?api-version=5.0' -f $($env:SYSTEM_TEAMFOUNDATIONSERVERURI), $($env:SYSTEM_TEAMPROJECTID), $($env:RELEASE_RELEASEID) )

$Release = Invoke-RestMethod -Uri $releaseurl -Headers @{
    Authorization = "Bearer $($env:SYSTEM_ACCESSTOKEN)"
}

$Release.variables.$apiAppServiceOutput.value = $tfOutputObject.api_app_services.value | ConvertTo-Json
$Release.variables.$pmsAppServiceOutput.value = $tfOutputObject.pms_app_services.value | ConvertTo-Json
$Release.variables.$portalAppServiceOutput.value = $tfOutputObject.portal_app_services.value | ConvertTo-Json
$Release.variables.$tenantIdOutput.value = $tfOutputObject.tenant_id.value
$Release.variables.$resourceGroupNameOutput.value = $tfOutputObject.resource_group_name.value
$Release.variables.$subscriptionIdOutput.value = $tfOutputObject.subscription_id.value

Write-Host ('Updating Release Pipeline Variables')
$json = @($Release) | ConvertTo-Json -Depth 99
Invoke-RestMethod -Uri $releaseurl -Method Put -Body $json -ContentType "application/json" -Headers @{Authorization = "Bearer $($env:SYSTEM_ACCESSTOKEN)" }

Write-Host ('Get updated Release Pipeline Variables')
$CheckRelease = Invoke-RestMethod -Uri $releaseurl -Headers @{
    Authorization = "Bearer $($env:SYSTEM_ACCESSTOKEN)"
}

# Write-Host ('Release Pipeline variables output: {0}' -f $($CheckRelease.variables | ConvertTo-Json -Depth 10))

#-------------------------------------------------------------------------------------------------------------------------------------------------------------------

# API Database Migration
$tenantId = $tfOutputObject.tenant_id.value
$subscriptionId = $tfOutputObject.subscription_id.value

$adminUsername = $tfOutputObject.sql_server_admin_username.value
$adminPassword = $tfOutputObject.sql_server_admin_password.value
$serverName = "$($tfOutputObject.sql_server_fully_qualified_domain_name.value).database.windows.net"

foreach ($tenant in $tfOutputObject.api_sql_databases.value.PSObject.Properties) {
    $databaseName = $tenant.Value.name

    $cmsMigrationPath = "$(System.DefaultWorkingDirectory)/iproxSource/iprox.open.api-migrations/cms/script.sql"
    Write-Host "Running CMS migration for $($tenant.Name)"

    Invoke-Sqlcmd -ServerInstance $serverName -Database $databaseName -Username $adminUsername -Password $adminPassword -InputFile $cmsMigrationPath

    $iproxMigrationPath = "$(System.DefaultWorkingDirectory)/iproxSource/iprox.open.api-migrations/iprox/script.sql"
    Write-Host "Running Iprox migration for $($tenant.Name)"

    Invoke-Sqlcmd -ServerInstance $serverName -Database $databaseName -Username $adminUsername -Password $adminPassword -InputFile $iproxMigrationPath
}

# CMS Database Migration
Set-Location ../../../iproxSource/iprox.open.pms-auth-migrations

$serverName = $tfOutputObject.sql_server_fully_qualified_domain_name.value

foreach ($tenant in $tfOutputObject.pms_auth_sql_databases.value.PSObject.Properties) {
    $databaseName = $tenant.Value.name

    $pmsDatabaseUrl = "sqlserver://$($serverName).database.windows.net:1433;database=$($databaseName);user=$($adminUsername)@$($serverName);password=$($adminPassword);encrypt=true;trustServerCertificate=false;hostNameInCertificate=*.database.windows.net;loginTimeout=30;"

    $env:PMS_DATABASE_URL = $pmsDatabaseUrl

    npx --yes prisma migrate deploy
}

