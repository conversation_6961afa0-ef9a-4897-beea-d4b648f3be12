function Invoke-TerraformDeploy {
  param(
    [Parameter(Mandatory = $true)]
    [string]$workingDirectory,

    [Parameter(Mandatory = $true)]
    [string]$workspace,

    [Parameter(Mandatory = $true)]
    [string]$varFile,

    [hashtable]$variables = @{},

    [string]$planArgs = "",

    [switch]$skipApply
  )

  Write-Info "Starting Terraform deployment in: $workingDirectory"
  Write-Info "Workspace: $workspace"
  Write-Info "Var file: $varFile"

  # Change to working directory
  Push-Location $workingDirectory

  try {
    # Step 1: Terraform Init
    Write-Info "Running terraform init..."
    terraform init
    if ($LASTEXITCODE -ne 0) {
      throw "Terraform init failed with exit code $LASTEXITCODE"
    }

    # Step 2: Select workspace
    Write-Info "Selecting workspace: $workspace"
    terraform workspace select $workspace
    if ($LASTEXITCODE -ne 0) {
      throw "Terraform workspace select failed with exit code $LASTEXITCODE"
    }

    # Step 3: Terraform Plan
    $planFile = "$workspace-tfplan"
    $planCommand = "terraform plan -var-file=`"$varFile`" -out `"$planFile`""

    # Add variables from hashtable
    foreach ($key in $variables.Keys) {
      $planCommand += " -var=`"$key=$($variables[$key])`""
    }

    # Add any additional plan arguments
    if ($planArgs) {
      $planCommand += " $planArgs"
    }

    Write-Info "Running terraform plan..."
    Write-Info "Command: $planCommand"
    Invoke-Expression $planCommand
    if ($LASTEXITCODE -ne 0) {
      throw "Terraform plan failed with exit code $LASTEXITCODE"
    }

    # Step 4: Terraform Apply (if not skipped)
    if (-not $skipApply) {
      Write-Info "Running terraform apply..."
      terraform apply "$planFile"
      if ($LASTEXITCODE -ne 0) {
        throw "Terraform apply failed with exit code $LASTEXITCODE"
      }
      Write-Info "Terraform deployment completed successfully!"
    }
    else {
      Write-Info "Terraform apply skipped (plan-only mode)"
    }
  }
  catch {
    Write-Error "Terraform deployment failed: $_"
    throw
  }
  finally {
    # Return to original directory
    Pop-Location
  }
}
