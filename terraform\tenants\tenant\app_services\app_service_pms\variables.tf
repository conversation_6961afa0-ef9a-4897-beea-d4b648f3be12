variable "tenants_configuration" {
  type = list(object(
    {
      name           = string
      deploymentRing = number
      shortname      = string
      plan           = string
      search         = string
      elasticpool    = string
      azureAd = list(object({
        id          = string
        name        = string
        instance    = string
        tenantId    = string
        clientId    = string
        pmsClientId = string
      }))
      apiCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      pmsCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      portalCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      frontdoorEndpoint = string
      apiIpAddresses    = list(string)
      pmsIpAddresses    = list(string)
      portalIpAddresses = list(string)
      corsUrls          = list(string)
  }))
  description = "this determines the names for all resources"
}


variable "tenant_app_service_plans" {
  description = "Map of App Service Plans to their tenants"
  type = map(object(
    {
      id = string
    }
  ))
}

variable "tenant_sql_database_pms_auths" {
  description = "Map of PMS Auth SQL Databases to their tenants"
  type = map(object(
    {
      name = string
    }
  ))
}

variable "tenant_application_insights_pmss" {
  description = "Map of API Application Insights to their tenants"
  type = map(object(
    {
      connection_string = string
    }
  ))
}

variable "tenant_app_service_apis" {
  description = "Map of App Service API to their tenants"
  type = map(object(
    {
      name                          = string
      default_hostname              = string
      principal_id                  = string
      custom_domain_verification_id = string
    }
  ))
}

variable "tenant_secrets" {
  description = "Map of Tenant Secrets to their tenants"
  type = map(object(
    {
      secret = string
    }
  ))
}

variable "app_service_pms_prefix" {
  type = string
}

variable "app_service_portal_prefix" {
  type = string
}

variable "resource_group_name" {
  type = string
}

variable "location" {
  type = string
}

variable "https_only" {
  type = string
}

variable "pms_node_version" {
  type = string
}

variable "pms_app_settings" {
  type = map(string)
}

variable "sql_server_name" {
  type = string
}

variable "sql_server_admin_username" {
  type = string
}

variable "sql_server_admin_password" {
  type = string
}

variable "tenant_alias_tag_name" {
  type = string
}

variable "tenant_shortname_tag_name" {
  type = string
}

variable "product_tag_name" {
  type = string
}

variable "env_tag_name" {
  type = string
}

variable "product_name" {
  type = string
}

variable "env" {
  type = string
}
