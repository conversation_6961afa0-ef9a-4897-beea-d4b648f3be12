param(
  [Parameter(Name = "ServicePrincipalId", Mandatory = $true)]
  [string]$servicePrincipalId,

  [Parameter(Name = "ServicePrincipalSecret", Mandatory = $true)]
  [string]$servicePrincipalSecret,

  [Parameter(Name = "TenantId", Mandatory = $true)]
  [string]$tenantId,

  [Parameter(Name = "SubscriptionId", Mandatory = $true)]
  [string]$subscriptionId,

  [Parameter(Name = "Environment", Mandatory = $true)]
  [string]$env,

  [Parameter(Name = "RingsToDeploy", Mandatory = $false)]
  [string]$ringsToDeploy = "[1]"
)

# Set up environment variables
$env:ARM_CLIENT_ID = $servicePrincipalId
$env:ARM_CLIENT_SECRET = $servicePrincipalSecret
$env:ARM_TENANT_ID = $tenantId
$env:ARM_SUBSCRIPTION_ID = $subscriptionId
