# param(
#   [Parameter(Mandatory = $true)]
#   [string]$servicePrincipalId,

#   [Parameter(Mandatory = $true)]
#   [string]$servicePrincipalSecret,

#   [Parameter(Mandatory = $true)]
#   [string]$tenantId,

#   [Parameter(Mandatory = $true)]
#   [string]$subscriptionId,

#   [Parameter(Mandatory = $true)]
#   [string]$env,

#   [Parameter(Mandatory = $false)]
#   [string]$ringsToDeploy = "[1]"
# )

# Set up environment variables
$env:ARM_CLIENT_ID = $servicePrincipalId
$env:ARM_CLIENT_SECRET = $servicePrincipalSecret
$env:ARM_TENANT_ID = $tenantId
$env:ARM_SUBSCRIPTION_ID = $subscriptionId
