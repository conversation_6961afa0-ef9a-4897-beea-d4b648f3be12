locals {
  azuread_settings = { for tenant in var.tenants_configuration : tenant.name => {
    settings = merge([
      for i, azureAd in tenant.azureAd : {
        "AuthenticationProviders__Azure__${i}__Instance" = azureAd.instance,
        "AuthenticationProviders__Azure__${i}__TenantId" = azureAd.tenantId,
        "AuthenticationProviders__Azure__${i}__ClientId" = azureAd.clientId
      }
    ]...)
  } }

  default_cors = { for tenant in var.tenants_configuration : tenant.name => {
    defaultCors = merge([
      for i, domain in tolist([tenant.pmsCustomDomain.hostName, tenant.portalCustomDomain.hostName]) : {
        "CorsUrls__${i}" = "https://${domain}"
      }
    ]...)
  } }
  dev_cors = { for tenant in var.tenants_configuration : tenant.name => {
    devCors = merge([
      for i, domain in tenant.corsUrls : {
        "CorsUrls__${length(local.default_cors[tenant.name].defaultCors) + i}" = "${domain}"
      }
    ]...)
  } }

  download_profile_settings = { for tenant in var.tenants_configuration : tenant.name => {
    profileSettings = merge([
      for profile_index, profile in var.download_settings.profiles : merge([
        for limit_index, limit in profile.speedLimits : {
          "DownloadSettings__Profiles__${profile_index}__Name"                                   = profile.name
          "DownloadSettings__Profiles__${profile_index}__SpeedLimits__${limit_index}__Speed"     = limit.speed,
          "DownloadSettings__Profiles__${profile_index}__SpeedLimits__${limit_index}__Threshold" = limit.threshold
        }
      ]...)
    ]...)
  } }
  download_unlimited_download_ips = { for tenant in var.tenants_configuration : tenant.name => {
    unlimitedDownloadIps = merge([
      for index, ip in var.download_settings.unlimitedDownloadIps : {
        "DownloadSettings__UnlimitedDownloadIps_${index}" = "${ip}"
      }
    ]...)
  } }
  download_disallowed_download_ips = { for tenant in var.tenants_configuration : tenant.name => {
    disallowedDownloadIps = merge([
      for index, ip in var.download_settings.disallowedDownloadIps : {
        "DownloadSettings__DisallowedDownloadIps_${index}" = "${ip}"
      }
    ]...)
  } }

  graph_api_settings = { for tenant in var.tenants_configuration : tenant.name => (
    tenant.graphApi != null ? {
      "GraphApi__TenantId"     = tenant.graphApi.tenantId
      "GraphApi__ClientId"     = tenant.graphApi.clientId
      "GraphApi__ClientSecret" = lookup(var.tenant_graph_api_secrets, tenant.name).secret
    } : {}
  ) }

  serilog_write_tos = {
    for tenant in var.tenants_configuration : tenant.name => {
      writeTos = merge([
        for write_index, write in var.serilog_write_to : merge({
          "Serilog__WriteTo__${write_index}__Name" = write.Name
          },
          (write.Args != null) ? merge(
            //Serilog__WriteTo__1__Args__ConnectionString is defined in app_settings
            (write.Args.TelemetryConverter != null) ? {
              "Serilog__WriteTo__${write_index}__Args__TelemetryConverter" = write.Args.TelemetryConverter
            } : {},
            (write.Args.WebHookUrl != null) ? {
              "Serilog__WriteTo__${write_index}__Args__WebHookUrl" = sensitive(write.Args.WebHookUrl)
            } : {},
            (write.Args.RestrictedToMinimumLevel != null) ? {
              "Serilog__WriteTo__${write_index}__Args__RestrictedToMinimumLevel" = write.Args.RestrictedToMinimumLevel
            } : {}
          ) : {}
        )
      ]...)
    }
  }
}

resource "azurerm_linux_web_app" "app-service-apis" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  name                = "${var.app_service_api_prefix}-${each.value.shortname}"
  location            = var.location
  resource_group_name = var.resource_group_name
  service_plan_id     = lookup(var.tenant_app_service_plans, "${each.value.plan}").id
  https_only          = true

  site_config {
    application_stack {
      dotnet_version = var.api_dotnet_version
    }

    ip_restriction_default_action = length(each.value.apiIpAddresses) != 0 ? "Deny" : "Allow"

    dynamic "ip_restriction" {
      for_each = each.value.apiIpAddresses
      content {
        ip_address = ip_restriction.value
        name       = ip_restriction.value
        priority   = 100
        action     = "Allow"
      }
    }
  }

  identity {
    type = "SystemAssigned"
  }

  app_settings = merge(
    var.api_app_settings,
    { # use dynammic blocks
      "Azure__KeyVaultUrl"                          = lookup(var.tenant_key_vaults, "${each.value.name}").vault_uri
      "Azure__BlobStorage__ContainerName"           = "${lookup(var.tenant_storage_containers, "${each.value.name}").name}"
      "Azure__BlobStorage__ConnectionString"        = "BlobEndpoint=https://${var.storage_account_name}.blob.core.windows.net/${var.storage_account_sas_token}"
      "Azure__BlobStorage__AdminConnectionString"   = "DefaultEndpointsProtocol=https;AccountName=${var.storage_account_name};AccountKey=${var.storage_account_access_key};EndpointSuffix=core.windows.net"
      "Azure__BlobStorage__ContainerUrl"            = "https://${var.storage_account_name}.blob.core.windows.net/${lookup(var.tenant_storage_containers, "${each.value.name}").name}/"
      "Azure__Queue__Name"                          = var.storage_queue_name
      "Azure__Queue__ConnectionString"              = "QueueEndpoint=https://${var.storage_account_name}.queue.core.windows.net/${var.storage_account_sas_token}"
      "OpenCms__EventDbConnection"                  = "Server=tcp:${var.sql_server_name}.database.windows.net,1433;Initial Catalog=${lookup(var.tenant_sql_database_apis, "${each.value.name}").name};Persist Security Info=False;User ID=${var.sql_server_admin_username};Password=${var.sql_server_admin_password};MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
      "Azure__CognitiveSearch__SearchServiceName"   = lookup(var.tenant_search_services, "${each.value.search}").name
      "Azure__CognitiveSearch__SearchServiceApiKey" = lookup(var.tenant_search_services, "${each.value.search}").primary_admin_key
      "Tenant__Name"                                = "${each.value.name}"
      "Tenant__ShortName"                           = "${each.value.shortname}"
      "Tenant__PortalUrl"                           = each.value.portalCustomDomain.provider != "adn-awn" ? "https://${each.value.portalCustomDomain.hostName}" : "https://${var.app_service_portal_prefix}-${each.value.shortname}.azurewebsites.net"
      "DownloadSettings__TimeLimit"                 = var.download_settings.timeLimit
      "Serilog__WriteTo__1__Args__ConnectionString" = lookup(var.tenant_application_insights_apis, "${each.value.name}").connection_string
      "ApplicationInsights__ConnectionString"       = lookup(var.tenant_application_insights_apis, "${each.value.name}").connection_string
      "DownloadTokenSettings__Issuer"               = var.download_token_issuer
      "DownloadTokenSettings__Audience"             = var.download_token_audience
      "DownloadTokenSettings__SecretKey"            = var.download_token_secret_key
    },
    local.azuread_settings[each.key].settings,
    local.default_cors[each.key].defaultCors,
    local.dev_cors[each.key].devCors,
    local.download_profile_settings[each.key].profileSettings,
    local.download_unlimited_download_ips[each.key].unlimitedDownloadIps,
    local.download_disallowed_download_ips[each.key].disallowedDownloadIps,
    local.serilog_write_tos[each.key].writeTos,
    local.graph_api_settings[each.key]
  )

  connection_string {
    name  = var.sql_connection_string_name
    type  = var.sql_connection_string_type
    value = "Server=tcp:${var.sql_server_name}.database.windows.net,1433;Initial Catalog=${lookup(var.tenant_sql_database_apis, "${each.value.name}").name};Persist Security Info=False;User ID=${var.sql_server_admin_username};Password=${var.sql_server_admin_password};MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
  }

  tags = {
    "${var.tenant_alias_tag_name}"     = "${each.value.name}"
    "${var.tenant_shortname_tag_name}" = "${each.value.shortname}"
    "${var.product_tag_name}"          = "${var.product_name}"
    "${var.env_tag_name}"              = "${var.env}"
  }
}
