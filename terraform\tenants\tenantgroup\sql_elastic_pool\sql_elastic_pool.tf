resource "azurerm_mssql_elasticpool" "sql-elastic-pool" {
  for_each = { for sql_elastic_pool_group in var.sql_elastic_pool_groups_configuration : sql_elastic_pool_group.name => sql_elastic_pool_group }

  name                = "${var.sql_elastic_pool_prefix}-${each.value.name}"
  resource_group_name = var.resource_group_name
  location            = var.location
  server_name         = var.sql_server_name
  max_size_gb         = 50
  enclave_type        = "VBS"

  sku {
    name     = each.value.sku
    tier     = each.value.tier
    capacity = each.value.capacity
  }

  per_database_settings {
    min_capacity = 0
    max_capacity = 10
  }
}
