locals {
  tenants_to_deploy_app_services = [
    for tenant in var.tenantsConfiguration : tenant if contains(var.ringsToDeploy, tenant.deploymentRing)
  ]
  tenants_configuration              = var.tenantsConfiguration
  resource_group_name                = var.resourceGroupName
  app_service_plan_prefix            = "${var.appServicePlanAbbreviation}-${var.productName}"
  app_service_prefix_api             = "${var.appServiceAbbreviation}-${var.productName}"
  app_service_prefix_pms             = "${var.appServiceAbbreviation}-${var.productName}-${var.pmsAppServiceSuffix}"
  app_service_prefix_portal          = "${var.appServiceAbbreviation}-${var.productName}-${var.portalAppServiceSuffix}"
  sql_database_prefix                = "${var.sqlDatabaseAbbreviation}-${var.productName}"
  storage_account_name               = "${var.storageAccountAbbreviation}${var.storageAccountName}"
  storage_container_prefix           = var.storageContainerName
  log_analytics_workspace_prefix     = "${var.logAnalyticsWorkspaceAbbreviation}-${var.productName}"
  search_service_prefix              = "${var.searchServiceAbbreviation}-${var.productName}"
  application_insights_prefix_api    = "${var.applicationInsightsAbbreviation}-${var.productName}"
  application_insights_prefix_pms    = "${var.applicationInsightsAbbreviation}-${var.productName}-${var.pmsAppServiceSuffix}"
  application_insights_prefix_portal = "${var.applicationInsightsAbbreviation}-${var.productName}-${var.portalAppServiceSuffix}"
  frontdoor_name                     = terraform.workspace == "dev" ? "${var.frontdoorAbbreviation}-${var.productName}-${terraform.workspace}" : "${var.frontdoorAbbreviation}-${var.productName}"
  key_vault_prefix                   = "${var.keyVaultAbbreviation}-${var.productName}"

  api_app_settings = {
    "ASPNETCORE_ENVIRONMENT"                     = var.aspNetCoreEnvironment
    "https_port"                                 = var.httpsPort
    "TenantId"                                   = var.tenantId
    "FileFilterSettings__Whitelist"              = jsonencode(var.fileFilterSettingsWhitelist)
    "FileFilterSettings__Blacklist"              = jsonencode(var.fileFilterSettingsBlacklist)
    "FileFilterSettings__ImageWhitelist"         = jsonencode(var.fileFilterSettingsImageWhitelist)
    "FileFilterSettings__FontWhitelist"          = jsonencode(var.fileFilterSettingsFontWhitelist)
    "HtmlSanitizerSettings__AllowedTags"         = jsonencode(var.htmlSanitizerSettingsAllowedTags)
    "HtmlSanitizerSettings__AllowedAttributes"   = jsonencode(var.htmlSanitizerSettingsAllowedAttributes)
    "HtmlSanitizerSettings__AllowedClasses"      = jsonencode(var.htmlSanitizerSettingsAllowedClasses)
    "HtmlSanitizerSettings__BlockedTags"         = jsonencode(var.htmlSanitizerSettingsBlockedTags)
    "Serilog__Using"                             = jsonencode(var.serilogUsing)
    "Serilog__MinimumLevel__Default"             = var.serilogMinimumLevelDefault
    "Serilog__MinimumLevel__Override__Microsoft" = var.serilogMinimumLevelOverrideMicrosoft
    "Serilog__MinimumLevel__Override__System"    = var.serilogMinimumLevelOverrideSystem
    "Serilog__Enrich"                            = jsonencode(var.serilogEnrich)
    "Azure__CognitiveSearch__HighlightFields"    = jsonencode(var.azureCognitiveSearchHighlightFields)
    "Azure__CognitiveSearch__HighlightPreTag"    = var.azureCognitiveSearchHighlightPreTag
    "Azure__CognitiveSearch__HighlightPostTag"   = var.azureCognitiveSearchHighlightPostTag
    "ApplicationInsightsAgent_EXTENSION_VERSION" = var.applicationInsightsAgentExtensionVersion
  }

  pms_app_settings = {
    "NEXTAUTH_SECRET"                            = var.nextAuthSecret
    "ApplicationInsightsAgent_EXTENSION_VERSION" = var.applicationInsightsAgentExtensionVersion
    "WEBSITE_RUN_FROM_PACKAGE"                   = 1
  }

  portal_app_settings = {
    "ApplicationInsightsAgent_EXTENSION_VERSION" = var.applicationInsightsAgentExtensionVersion
  }
}

// Existing ----------------------------------------------------------------------------------------------------

data "azurerm_subscription" "subscription" {
  subscription_id = "e3cff265-5d46-41fb-93f8-c3757a37d57a"
}

data "azurerm_resource_group" "resource-group" {
  name = local.resource_group_name
}

// Shared ------------------------------------------------------------------------------------------------------
data "terraform_remote_state" "shared_state" {
  backend   = "azurerm"
  workspace = "tenants" # This is set to tenants because the shared state is deployed to the tenants workspace
  config = {
    resource_group_name  = "iprox.open"
    storage_account_name = "iproxopen"
    container_name       = "iprox-open-tfstate"
    key                  = "iprox-open-tenants-shared.tfstate"
  }
}

data "terraform_remote_state" "tenantgroup_state" {
  backend   = "azurerm"
  workspace = "tenants" # This is set to tenants because the tenantgroup state is deployed to the tenants workspace
  config = {
    resource_group_name  = "iprox.open"
    storage_account_name = "iproxopen"
    container_name       = "iprox-open-tfstate"
    key                  = "iprox-open-tenants-tenantgroup.tfstate"
  }
}

// -------------------------------------------------------------------------------------------------------------

module "tenant_secrets" {
  source = "./key_vault_tenant_secrets"

  tenants_configuration = var.tenantsConfiguration

  resource_group_name            = data.azurerm_resource_group.resource-group.name
  tenant_secrets_key_vault_name  = var.tenantSecretsKeyVaultName
  aad_client_app_secret_prefix   = var.aadClientAppSecretPrefix
  graph_api_client_secret_prefix = var.graphApiClientSecretPrefix
}

// Tenant Specific ---------------------------------------------------------------------------------------------

// Key Vault
module "key-vault" {
  source = "./key_vault"

  tenants_configuration = local.tenants_configuration

  key_vault_prefix    = local.key_vault_prefix
  location            = var.location
  resource_group_name = data.azurerm_resource_group.resource-group.name
  tenant_id           = var.tenantId
  sku                 = "standard"


  tenant_alias_tag_name     = var.tenantAliasTagName
  tenant_shortname_tag_name = var.tenantShortnameTagName
  product_tag_name          = var.productTagName
  env_tag_name              = var.envTagName
  product_name              = var.productName
  env                       = var.env
}

// Sql Database Backend
module "sql-database-api" {
  source = "./sql_databases/sql_database_api"

  tenants_configuration     = local.tenants_configuration
  tenants_sql_elastic_pools = data.terraform_remote_state.tenantgroup_state.outputs.sql_elastic_pool.sql_elastic_pool_params

  sql_database_prefix  = local.sql_database_prefix
  sql_server_id        = data.terraform_remote_state.shared_state.outputs.sql_server.id
  collation            = "SQL_Latin1_General_CP1_CI_AS"
  max_size_gb          = var.sqlMaxSizeGigabytes
  sku                  = var.sqlSkuName
  read_scale           = false
  zone_redundant       = false
  geo_backup_enabled   = true
  storage_account_type = "Geo"

  tenant_alias_tag_name     = var.tenantAliasTagName
  tenant_shortname_tag_name = var.tenantShortnameTagName
  product_tag_name          = var.productTagName
  env_tag_name              = var.envTagName
  product_name              = var.productName
  env                       = var.env
}

// Sql Database PMS Auth
module "sql-database-pms-auth" {
  source = "./sql_databases/sql_database_pms_auth"

  tenants_configuration     = local.tenants_configuration
  tenants_sql_elastic_pools = data.terraform_remote_state.tenantgroup_state.outputs.sql_elastic_pool.sql_elastic_pool_params

  sql_database_prefix  = local.sql_database_prefix
  sql_server_id        = data.terraform_remote_state.shared_state.outputs.sql_server.id
  collation            = "SQL_Latin1_General_CP1_CI_AS"
  max_size_gb          = var.sqlMaxSizeGigabytes
  sku                  = var.sqlSkuName
  read_scale           = false
  zone_redundant       = false
  geo_backup_enabled   = true
  storage_account_type = "Geo"

  tenant_alias_tag_name     = var.tenantAliasTagName
  tenant_shortname_tag_name = var.tenantShortnameTagName
  product_tag_name          = var.productTagName
  env_tag_name              = var.envTagName
  product_name              = var.productName
  env                       = var.env
}


// Application Insights
module "application-insights-api" {
  source = "./application_insights/application_insights_api"

  tenants_configuration = local.tenants_configuration

  application_insights_api_prefix = local.application_insights_prefix_api
  location                        = var.location
  resource_group_name             = data.azurerm_resource_group.resource-group.name
  application_type                = "web"
  workspace_id                    = data.terraform_remote_state.shared_state.outputs.log_analytics_workspace.id
  tenant_alias_tag_name           = var.tenantAliasTagName
  tenant_shortname_tag_name       = var.tenantShortnameTagName
  product_tag_name                = var.productTagName
  env_tag_name                    = var.envTagName
  product_name                    = var.productName
  env                             = var.env
}


module "application-insights-pms" {
  source = "./application_insights/application_insights_pms"

  tenants_configuration = local.tenants_configuration

  application_insights_pms_prefix = local.application_insights_prefix_pms
  location                        = var.location
  resource_group_name             = data.azurerm_resource_group.resource-group.name
  application_type                = "web"
  workspace_id                    = data.terraform_remote_state.shared_state.outputs.log_analytics_workspace.id
  tenant_alias_tag_name           = var.tenantAliasTagName
  tenant_shortname_tag_name       = var.tenantShortnameTagName
  product_tag_name                = var.productTagName
  env_tag_name                    = var.envTagName
  product_name                    = var.productName
  env                             = var.env
}


module "application-insights-portal" {
  source = "./application_insights/application_insights_portal"

  tenants_configuration = local.tenants_configuration

  application_insights_portal_prefix = local.application_insights_prefix_portal
  location                           = var.location
  resource_group_name                = data.azurerm_resource_group.resource-group.name
  application_type                   = "web"
  workspace_id                       = data.terraform_remote_state.shared_state.outputs.log_analytics_workspace.id
  tenant_alias_tag_name              = var.tenantAliasTagName
  tenant_shortname_tag_name          = var.tenantShortnameTagName
  product_tag_name                   = var.productTagName
  env_tag_name                       = var.envTagName
  product_name                       = var.productName
  env                                = var.env
}

// Storage Container
module "storage-container" {
  source = "./storage_container"

  tenants_configuration = local.tenants_configuration

  storage_container_prefix = local.storage_container_prefix
  storage_account_name     = data.terraform_remote_state.shared_state.outputs.storage_account.name
  container_access_type    = "private"
}

// Storage Container SAS token
module "storage-container-sas" {
  source = "./storage_container_sas"

  tenants_configuration     = local.tenants_configuration
  tenant_storage_containers = module.storage-container.storage_container_params

  connection_string = data.terraform_remote_state.shared_state.outputs.storage_account.primary_connection_string
  https_only        = true
  // expired dates, so is it actually in use?
  start_date        = "2023-03-21T00:00:00Z"
  expiry_date       = "2024-03-21T00:00:00Z"
  read_permission   = true
  write_permission  = true
  delete_permission = true
  list_permission   = true
  add_permission    = true
  create_permission = true
}

// Storage Account SAS token
module "storage-account-sas" {
  source = "./storage_account_sas"

  connection_string = data.terraform_remote_state.shared_state.outputs.storage_account.primary_connection_string
  https_only        = true

  resource_types_service   = true
  resource_types_container = true
  resource_types_object    = true

  services_blob  = true # TODO: Change to false and use sas token
  services_queue = true
  services_table = false
  services_file  = true

  start_date         = "2024-10-01T00:00:00Z" # TODO: Use a sliding date for the start and expiry dates
  expiry_date        = "2025-10-01T00:00:00Z"
  read_permission    = true
  write_permission   = true
  delete_permission  = true
  list_permission    = true
  add_permission     = true
  create_permission  = true
  update_permission  = true
  process_permission = true
  tag_permission     = true
  filter_permission  = true
}

// App Services
module "app-service-api" {
  source = "./app_services/app_service_api"

  tenants_configuration            = local.tenants_configuration
  tenant_app_service_plans         = data.terraform_remote_state.tenantgroup_state.outputs.app_service_plan.app_service_plan_params
  tenant_search_services           = data.terraform_remote_state.tenantgroup_state.outputs.search_service.search_service_params
  tenant_application_insights_apis = module.application-insights-api.application_insights_api_params
  tenant_storage_containers        = module.storage-container.storage_container_params
  tenant_sql_database_apis         = module.sql-database-api.sql_database_api_params
  tenant_key_vaults                = module.key-vault.key_vault_params
  tenant_graph_api_secrets         = module.tenant_secrets.tenant_graph_api_secrets_params

  storage_account_name       = data.terraform_remote_state.shared_state.outputs.storage_account.name
  storage_account_access_key = data.terraform_remote_state.shared_state.outputs.storage_account.primary_access_key
  storage_queue_name         = data.terraform_remote_state.shared_state.outputs.storage_queue.name
  storage_account_sas_token  = module.storage-account-sas.sas
  sql_server_name            = data.terraform_remote_state.shared_state.outputs.sql_server.fully_qualified_domain_name
  sql_server_admin_username  = data.terraform_remote_state.shared_state.outputs.sql_server.admin_username
  sql_server_admin_password  = data.terraform_remote_state.shared_state.outputs.sql_server.admin_password

  app_service_api_prefix        = local.app_service_prefix_api
  app_service_portal_prefix     = local.app_service_prefix_portal
  location                      = var.location
  resource_group_name           = data.azurerm_resource_group.resource-group.name
  https_only                    = true
  api_dotnet_version            = var.apiDotnetVersion
  cognitive_search_index_prefix = var.azureCognitiveSearchIndexPrefix

  api_app_settings           = local.api_app_settings
  sql_connection_string_name = var.iproxOpenSqlDbName
  sql_connection_string_type = var.iproxOpenSqlDbType

  tenant_alias_tag_name     = var.tenantAliasTagName
  tenant_shortname_tag_name = var.tenantShortnameTagName
  product_tag_name          = var.productTagName
  env_tag_name              = var.envTagName
  product_name              = var.productName
  env                       = var.env
  download_settings         = var.downloadSettings
  serilog_write_to          = var.serilogWriteTo

  download_token_issuer     = var.downloadTokenIssuer
  download_token_audience   = var.downloadTokenAudience
  download_token_secret_key = var.downloadTokenSecretKey
}

module "app-service-pms" {
  source = "./app_services/app_service_pms"

  tenants_configuration            = local.tenants_configuration
  tenant_app_service_plans         = data.terraform_remote_state.tenantgroup_state.outputs.app_service_plan.app_service_plan_params
  tenant_application_insights_pmss = module.application-insights-pms.application_insights_pms_params
  tenant_app_service_apis          = module.app-service-api.app_service_api_params
  tenant_sql_database_pms_auths    = module.sql-database-pms-auth.sql_database_pms_auth_params
  tenant_secrets                   = module.tenant_secrets.tenant_secrets_params

  sql_server_name           = data.terraform_remote_state.shared_state.outputs.sql_server.fully_qualified_domain_name
  sql_server_admin_username = data.terraform_remote_state.shared_state.outputs.sql_server.admin_username
  sql_server_admin_password = data.terraform_remote_state.shared_state.outputs.sql_server.admin_password

  app_service_pms_prefix    = local.app_service_prefix_pms
  app_service_portal_prefix = local.app_service_prefix_portal
  location                  = var.location
  resource_group_name       = data.azurerm_resource_group.resource-group.name
  https_only                = true
  pms_node_version          = var.pmsNodeVersion

  pms_app_settings = local.pms_app_settings

  tenant_alias_tag_name     = var.tenantAliasTagName
  tenant_shortname_tag_name = var.tenantShortnameTagName
  product_tag_name          = var.productTagName
  env_tag_name              = var.envTagName
  product_name              = var.productName
  env                       = var.env
}

module "app-service-portal" {
  source = "./app_services/app_service_portal"

  tenants_configuration               = local.tenants_configuration
  tenant_app_service_plans            = data.terraform_remote_state.tenantgroup_state.outputs.app_service_plan.app_service_plan_params
  tenant_application_insights_portals = module.application-insights-portal.application_insights_portal_params
  tenant_app_service_apis             = module.app-service-api.app_service_api_params

  app_service_portal_prefix = local.app_service_prefix_portal
  location                  = var.location
  resource_group_name       = data.azurerm_resource_group.resource-group.name
  https_only                = true
  portal_node_version       = var.portalNodeVersion

  portal_app_settings = local.portal_app_settings

  tenant_alias_tag_name     = var.tenantAliasTagName
  tenant_shortname_tag_name = var.tenantShortnameTagName
  product_tag_name          = var.productTagName
  env_tag_name              = var.envTagName
  product_name              = var.productName
  env                       = var.env
}

# module "app_service_custom_hostname_binding" {
#   source = "./app_service_custom_hostname_binding"

#   tenants_configuration      = local.tenants_configuration
#   tenant_app_service_apis    = module.app-service-api.app_service_api_params
#   tenant_app_service_pmss    = module.app-service-pms.app_service_pms_params
#   tenant_app_service_portals = module.app-service-portal.app_service_portal_paramss

// Key Vault Access Policies
module "key-vault-access-policy" {
  source = "./key_vault_access_policy"

  tenants_configuration = local.tenants_configuration

  tenant_app_service_apis = module.app-service-api.app_service_api_params
  tenant_app_service_pmss = module.app-service-pms.app_service_pms_params
  tenant_key_vaults       = module.key-vault.key_vault_params

  tenant_id = var.tenantId

  key_vault_key_permissions_full         = var.keyVaultKeyPermissionsFull
  key_vault_secret_permissions_full      = var.keyVaultSecretPermissionsFull
  key_vault_certificate_permissions_full = var.keyVaultCertificatePermissionsFull
  key_vault_storage_permissions_full     = var.keyVaultStoragePermissionsFull

  depends_on = [
    module.key-vault,
  ]
}

// Key Vault Secrets
module "key-vault-secret" {
  source = "./key_vault_secret"

  tenants_configuration    = local.tenants_configuration
  tenant_sql_database_apis = module.sql-database-api.sql_database_api_params
  tenant_key_vaults        = module.key-vault.key_vault_params

  sql_connection_string_key = var.iproxOpenSqlDbName
  sql_server_name           = data.terraform_remote_state.shared_state.outputs.sql_server.fully_qualified_domain_name
  sql_server_admin_username = data.terraform_remote_state.shared_state.outputs.sql_server.admin_username
  sql_server_admin_password = data.terraform_remote_state.shared_state.outputs.sql_server.admin_password

  depends_on = [
    module.key-vault,
    module.key-vault-access-policy,
  ]
}

// Frontdoor resources
module "frontdoor-resources" {
  source                         = "./frontdoor_resources"
  tenantgroup_frontdoor_endpoint = data.terraform_remote_state.tenantgroup_state.outputs.frontdoor-endpoint.frontdoor_endpoint_params
  tenants_configuration          = local.tenants_configuration
  tenant_app_service_apis        = module.app-service-api.app_service_api_params
  tenant_app_service_pmss        = module.app-service-pms.app_service_pms_params
  tenant_app_service_portals     = module.app-service-portal.app_service_portal_params

  frontdoor_id                  = data.terraform_remote_state.shared_state.outputs.frontdoor.id
  frontdoor_origin_group_prefix = var.frontDoorOriginGroupPrefix
  frontdoor_origin_prefix       = var.frontDoorOriginPrefix
  frontdoor_route_prefix        = var.frontDoorRoutePrefix
}

// Frontdoor Endpoint
# module "frontdoor-endpoint" {
#   source = "./modules/tenantgroup/frontdoor_endpoint"

#   frontdoor_endpoint_configuration = local.frontdoor_endpoint_configuration
#   frontdoor_profile_id             = module.frontdoor.id
#   frontdoor_endpoint_prefix        = var.frontDoorEndpointPrefix

#   depends_on = [module.frontdoor]
# }
// -------------------------------------------------------------------------------------------------------------
