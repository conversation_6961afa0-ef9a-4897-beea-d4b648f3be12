output "app_service_portal_params" {
  description = "Map of Portal Service Parameters to their tenants"
  value = { for tenant_name, app_service_portal in azurerm_linux_web_app.app-service-portals :
    tenant_name => {
      name                          = app_service_portal.name
      default_hostname              = app_service_portal.default_hostname
      principal_id                  = app_service_portal.identity[0].principal_id
      custom_domain_verification_id = app_service_portal.custom_domain_verification_id
    }
  }
}
