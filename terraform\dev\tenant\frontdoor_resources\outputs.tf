// Commented out as it is not needed at the moment
# output "frontdoor_origin_group_params" {
#   description = "Map of Frontdoor Origin Groups to their tenants"
#   value = { for tenant_name, frontdoor_origin_group in azurerm_cdn_frontdoor_origin_group.frontdoor-origin-groups :
#     tenant_name => {
#       id = frontdoor_origin_group.id
#     }
#   }
# }

# output "frontdoor_origin_params" {
#   description = "Map of Frontdoor Origins to their tenants"
#   value = { for tenant_name, frontdoor_origin in azurerm_cdn_frontdoor_origin.frontdoor-origins :
#     tenant_name => {
#       id = frontdoor_origin.id
#     }
#   }
# }

# output "frontdoor_endpoint_params" {
#   description = "Map of Frontdoor Endpoints to their tenants"
#   value = { for tenant_name, frontdoor_endpoint in azurerm_cdn_frontdoor_endpoint.frontdoor-endpoints :
#     tenant_name => {
#       id = frontdoor_endpoint.id
#     }
#   }
# }

# output "frontdoor_route_params" {
#   description = "Map of Frontdoor Routes to their tenants"
#   value = { for tenant_name, frontdoor_route in azurerm_cdn_frontdoor_route.frontdoor-routes :
#     tenant_name => {
#       id = frontdoor_route.id
#     }
#   }
# }
