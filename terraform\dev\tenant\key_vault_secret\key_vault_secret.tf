resource "azurerm_key_vault_secret" "key-vault-secret" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  name         = var.sql_connection_string_key
  value        = "Server=tcp:${var.sql_server_name}.database.windows.net,1433;Initial Catalog=${lookup(var.tenant_sql_database_apis, "${each.value.name}").name};Persist Security Info=False;User ID=${var.sql_server_admin_username};Password=${var.sql_server_admin_password};MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
  key_vault_id = lookup(var.tenant_key_vaults, "${each.value.name}").id
}