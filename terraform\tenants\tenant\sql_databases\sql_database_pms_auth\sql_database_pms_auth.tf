resource "azurerm_mssql_database" "sql-database-pms-auth" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  name                 = "${var.sql_database_prefix}-${each.value.shortname}-pms-auth"
  server_id            = var.sql_server_id
  collation            = var.collation
  max_size_gb          = var.max_size_gb
  sku_name             = "ElasticPool"
  read_scale           = var.read_scale
  zone_redundant       = var.zone_redundant
  geo_backup_enabled   = var.geo_backup_enabled
  storage_account_type = var.storage_account_type
  elastic_pool_id      = lookup(var.tenants_sql_elastic_pools, "${each.value.elasticpool}").id
  enclave_type         = "VBS"

  short_term_retention_policy {
    retention_days = 7
  }

  long_term_retention_policy {
    weekly_retention  = "P4W"
    monthly_retention = "P1M"
    yearly_retention  = "P1Y"
    week_of_year      = 1
  }


  tags = {
    "${var.tenant_alias_tag_name}"     = "${each.value.name}"
    "${var.tenant_shortname_tag_name}" = "${each.value.shortname}"
    "${var.product_tag_name}"          = "${var.product_name}"
    "${var.env_tag_name}"              = "${var.env}"
  }

  lifecycle {
    prevent_destroy = true
  }
}

# weekly_retention - (Optional) The weekly retention policy for an LTR backup in an ISO 8601 format. Valid value is between 1 to 520 weeks. e.g. P1Y, P1M, P1W or P7D.

# monthly_retention - (Optional) The monthly retention policy for an LTR backup in an ISO 8601 format. Valid value is between 1 to 120 months. e.g. P1Y, P1M, P4W or P30D.

# yearly_retention - (Optional) The yearly retention policy for an LTR backup in an ISO 8601 format. Valid value is between 1 to 10 years. e.g. P1Y, P12M, P52W or P365D.

# week_of_year - (Optional) The week of year to take the yearly backup in an ISO 8601 format. Value has to be between 1 and 52.
