# script to delete resources created for a PR environment 'manually' (not using the PR workflow)
param(
  [Parameter(Mandatory=$true)]
  [string]$prNumber
)

$subscriptionId = 'cd48a980-2e56-4b29-9152-03e484d091ea'
$resourceGroup = 'iprox.open'

az account set --subscription $subscriptionId

az keyvault delete --name "kv-iprox-open-$prNumber" --resource-group $resourceGroup

az monitor app-insights component delete --app "ai-iprox-open-$prNumber" --resource-group $resourceGroup
az monitor app-insights component delete --app "ai-iprox-open-pms-$prNumber" --resource-group $resourceGroup
az monitor app-insights component delete --app "ai-iprox-open-portal-$prNumber" --resource-group $resourceGroup

az sql db delete --name "sqldb-iprox-open-$prNumber" --server "sql-iprox-open-dev" --resource-group $resourceGroup
az sql db delete --name "sqldb-iprox-open-$prNumber-pms-auth" --server "sql-iprox-open-dev" --resource-group $resourceGroup

az search service delete --name "srch-iprox-open-pr$prNumber" --resource-group $resourceGroup

az webapp delete --name "app-iprox-open-$prNumber" --resource-group $resourceGroup
az webapp delete --name "app-iprox-open-pms-$prNumber" --resource-group $resourceGroup
az webapp delete --name "app-iprox-open-portal-$prNumber" --resource-group $resourceGroup
