variable "tenants_configuration" {
  type = list(object(
    {
      name           = string
      deploymentRing = number
      shortname      = string
      plan           = string
      search         = string
      elasticpool    = string
      azureAd = list(object({
        id          = string
        name        = string
        instance    = string
        tenantId    = string
        clientId    = string
        pmsClientId = string
      }))
      graphApi = optional(object({
        tenantId = string
        clientId = string
      }))
      apiCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      pmsCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      portalCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      frontdoorEndpoint = string
      apiIpAddresses    = list(string)
      pmsIpAddresses    = list(string)
      portalIpAddresses = list(string)
      corsUrls          = list(string)
  }))
  description = "this determines the names for all resources"
}

variable "tenant_secrets_key_vault_name" {
  type = string
}

variable "aad_client_app_secret_prefix" {
  type = string
}

variable "graph_api_client_secret_prefix" {
  type = string
}

variable "resource_group_name" {
  type = string
}
