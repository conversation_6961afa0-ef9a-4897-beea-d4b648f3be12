variable "tenants_configuration" {
  type = list(object(
    {
      name           = string
      deploymentRing = number
      shortname      = string
      plan           = string
      search         = string
      elasticpool    = string
      azureAd = list(object({
        id          = string
        name        = string
        instance    = string
        tenantId    = string
        clientId    = string
        pmsClientId = string
      }))
      apiCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      pmsCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      portalCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      frontdoorEndpoint = string
      apiIpAddresses    = list(string)
      pmsIpAddresses    = list(string)
      portalIpAddresses = list(string)
      corsUrls          = list(string)
  }))
  description = "this determines the names for all resources"
}

variable "tenant_app_service_apis" {
  description = "Map of App Service API to their tenants"
  type = map(object(
    {
      name                          = string
      default_hostname              = string
      principal_id                  = string
      custom_domain_verification_id = string
    }
  ))
}

variable "tenant_app_service_pmss" {
  description = "Map of App Service PMS to their tenants"
  type = map(object(
    {
      name                          = string
      default_hostname              = string
      principal_id                  = string
      custom_domain_verification_id = string
    }
  ))
}

variable "tenant_app_service_portals" {
  description = "Map of App Service Portal to their tenants"
  type = map(object(
    {
      name                          = string
      default_hostname              = string
      principal_id                  = string
      custom_domain_verification_id = string
    }
  ))
}

variable "resource_group_name" {
  type = string
}
