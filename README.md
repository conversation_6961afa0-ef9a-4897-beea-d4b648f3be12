# Introduction
This Terraform project set manages the infrastructure for iprox.open. The infrastructure is managed in three stages:
- PR environments
- dev environments
- production environments

The Terraform code is split into two global 'projects', `dev` and `tenants`.
Note: all code is duplicated along the different 'modules', we should use Terragrunt to solve this issue in the future.

- The infrastructure for PR environments is build by the `dev` project and the vars are prepared by the
"\iprox.open\scripts\open-prs.ps1" script from the iprox.open repository.

- The infrastructure for dev environments is build by the `dev` project and dev.tfvars.

- The infrastructure for production environments is build by the `tenants` project and tenants.tfvars.

# Getting Started
Make sure you have installed azure-cli and terraform.
If you use chocolately you can do the following command:
```
choco install azure-cli
choco install terraform
choco install vscode-terraform -y
```
Make sure you are logged in with `az login`.

# Managing projects and subscriptions
The `dev` and `tenants` projects both use a different subscription.

When working in `dev` use:
```
az account set --subscription "cd48a980-2e56-4b29-9152-03e484d091ea"
```

and always select the right workspace:
```
terraform workspace select dev
```
or
```
terraform workspace select pr
```

When working in `tenants` use:
```
az account set --subscription "e3cff265-5d46-41fb-93f8-c3757a37d57a"
```

and always select the right workspace:
```
terraform workspace select tenants
```

# Creating shared resources
The shared infrastructure resources like SQL Servers, App Servie Plans, Search Services are created from the 'tenantgroup' projects (in `dev` and `tenants`).

For `dev`
```
az account set --subscription "cd48a980-2e56-4b29-9152-03e484d091ea"
terraform init
terraform workspace select dev
terraform plan -var-file="$(terraform workspace show).tfvars" -out "$(terraform workspace show)-tfplan"
```
When you want to deploy the changes you can use the following command:
```
terraform apply "dev-tfplan"
```

For `tenants`
```
az account set --subscription "e3cff265-5d46-41fb-93f8-c3757a37d57a"
terraform init
terraform workspace select tenants
terraform plan -var-file="$(terraform workspace show).tfvars" -out "$(terraform workspace show)-tfplan"
```
When you want to deploy the changes you can use the following command:
```
terraform apply "tenants-tfplan"
```

# Build and Test
When you start developing in the 'terraform/dev/tenant' folder you can use the following commands to start:
```
az account set --subscription "cd48a980-2e56-4b29-9152-03e484d091ea"
terraform init
terraform workspace select dev
terraform plan -var-file="$(terraform workspace show).tfvars" -var="ringsToDeploy=[1]" -out "$(terraform workspace show)-tfplan"
```
When you want to deploy the changes you can use the following command:
```
terraform apply "dev-tfplan"
```

# Updating PR enviroments
The pr.tfvars file for PR environments is dynamically modified to reflect the current open PR's, by adding `tenantsConfiguration = [...]`.
The pipeline setup in Azure does not recognize completing or abandoning a PR as a change and therefore does not start a 'redeploy' to remove
the PR environment. Upon the next _active_ PR change the pipeline will run again and thus the removal ofthe stale PR environments in initiated.
Adding `tenantsConfiguration = []` to pr.tfvars and plan/apply will remove all PR environments.
```
az account set --subscription "cd48a980-2e56-4b29-9152-03e484d091ea"
terraform init
terraform workspace select pr
terraform plan -var-file="$(terraform workspace show).tfvars" -var="ringsToDeploy=[1]" -out "$(terraform workspace show)-tfplan"
```
When you want to deploy the changes you can use the following command:
```
terraform apply "pr-tfplan"
```

# Adding a new tenant
To add a new tenant open the file \terraform\tenants\tenant\tenants.tfvars and add a new entry to the `tenantsConfiguration` array.
Per AzureAd client create a secret in the kv-tenant-secrets Key Vault.
In the \terraform\tenants\tenant folder:
```
az account set --subscription "e3cff265-5d46-41fb-93f8-c3757a37d57a"
terraform init
terraform workspace select tenants
terraform plan -var-file="$(terraform workspace show).tfvars" -var="ringsToDeploy=[1]" -out "$(terraform workspace show)-tfplan"
```
When you want to deploy the changes you can use the following command:
```
terraform apply "tenants-tfplan"
```

# Contribute
Make sure you always do a format before pushing any changes:

```
terraform fmt -recursive
```
