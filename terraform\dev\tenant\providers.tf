terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.117.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.7.0"
    }
  }

  backend "azurerm" {
    resource_group_name  = "iprox.open"
    storage_account_name = "iproxopentf"
    container_name       = "iprox-open-tfstate"
    key                  = "iprox-open-dev-tenant.tfstate"
  }

  required_version = ">= 1.6.0"
}

provider "azurerm" {
  features {}
  use_oidc = true
}
