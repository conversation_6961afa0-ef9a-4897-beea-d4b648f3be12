variable "tenants_configuration" {
  type = list(object(
    {
      name           = string
      deploymentRing = number
      shortname      = string
      plan           = string
      search         = string
      elasticpool    = string
      azureAd = list(object({
        id          = string
        name        = string
        instance    = string
        tenantId    = string
        clientId    = string
        pmsClientId = string
      }))
      apiCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      pmsCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      portalCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      frontdoorEndpoint = string
      apiIpAddresses    = list(string)
      pmsIpAddresses    = list(string)
      portalIpAddresses = list(string)
      corsUrls          = list(string)
  }))
  description = "this determines the names for all resources"
}

variable "tenant_sql_database_apis" {
  description = "Map of SQL Databases to their tenants"
  type = map(object(
    {
      name = string
    }
  ))
}

variable "tenant_key_vaults" {
  description = "Map of Key Vaults to their tenants"
  type = map(object(
    {
      id        = string
      vault_uri = string
    }
  ))
}

variable "sql_connection_string_key" {
  type = string
}

variable "sql_server_name" {
  type = string
}

variable "sql_server_admin_username" {
  type = string
}

variable "sql_server_admin_password" {
  type = string
}
