tenantsConfiguration = [
  {
    name           = "woo"
    deploymentRing = 1
    shortname      = "woo"
    plan           = "one"
    search         = "01"
    elasticpool    = "pool01"
    azureAd = [
      {
        id          = "azure-ad"
        name        = "Microsoft Entra ID iprox."
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "1036265b-2bd7-4019-a418-1b3651df7347"
        clientId    = "iprox.open.dev"
        pmsClientId = "da29d267-2430-4b27-ac68-2cd5a6aa5df0"
      },
      {
        id          = "azure-ad-iprox"
        name        = "iprox.support"
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "1036265b-2bd7-4019-a418-1b3651df7347"
        clientId    = "iprox.open.support"
        pmsClientId = "cb6a3cee-63e4-4a5b-94fb-582159be7e6d"
    }]
    graphApi = {
      tenantId : "1036265b-2bd7-4019-a418-1b3651df7347"
      clientId : "31de4a52-dbed-4ddc-9850-72d6fd8160a5"
    }
    apiCustomDomain = {
      hostName = "api.iprox-open.nl" # "https://api-woo.iprox.nl",
      provider = "adn-afd"
    }
    pmsCustomDomain = {
      hostName = "beheer.iprox-open.nl" # "https://beheer-woo.iprox.nl",
      provider = "adn-afd"
    }
    portalCustomDomain = {
      hostName = "portaal.iprox-open.nl" # https://open-woo.iprox.nl",
      provider = "adn-afd"
    }
    frontdoorEndpoint = "one"
    apiIpAddresses    = []
    pmsIpAddresses    = []
    portalIpAddresses = []
    corsUrls          = ["https://infoprojectshq.sharepoint.com"]
  },
  {
    name           = "vechtstromen"
    deploymentRing = 2
    shortname      = "wve"
    plan           = "02"
    search         = "01"
    elasticpool    = "pool01"
    azureAd = [
      {
        id          = "azure-ad"
        name        = "Microsoft Entra ID waterschap Vechtstromen"
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "7b2d475a-6e57-4cf4-ac6d-8290592424cd"
        clientId    = "iprox.open"
        pmsClientId = "fbc3998a-9598-4ca7-b2e7-6d61ec052554"
      },
      {
        id          = "azure-ad-iprox"
        name        = "iprox.support"
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "1036265b-2bd7-4019-a418-1b3651df7347"
        clientId    = "iprox.open.support"
        pmsClientId = "cb6a3cee-63e4-4a5b-94fb-582159be7e6d"
    }]
    apiCustomDomain = {
      hostName = "api-vechtstromen.iprox-open.nl",
      provider = "adn-afd"
    }
    pmsCustomDomain = {
      hostName = "beheer-vechtstromen.iprox-open.nl",
      provider = "adn-afd"
    }
    portalCustomDomain = {
      hostName = "open.vechtstromen.nl",
      provider = "adn-afd"
    }
    frontdoorEndpoint = "one"
    apiIpAddresses    = []
    pmsIpAddresses    = []
    portalIpAddresses = []
    corsUrls          = ["https://open.vechtstromen.nl"]
  },
  {
    name           = "waterschap-limburg"
    deploymentRing = 2
    shortname      = "wsl"
    plan           = "02"
    search         = "01"
    elasticpool    = "pool01"
    azureAd = [
      {
        id          = "azure-ad"
        name        = "Microsoft Entra ID waterschap Limburg"
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "507015cf-5dd2-46ec-8df0-c567c092cd74"
        clientId    = "iprox.open"
        pmsClientId = "5baadc2a-69da-4ce2-83b5-4e36785cda8e"
      },
      {
        id          = "azure-ad-iprox"
        name        = "iprox.support"
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "1036265b-2bd7-4019-a418-1b3651df7347"
        clientId    = "iprox.open.support"
        pmsClientId = "cb6a3cee-63e4-4a5b-94fb-582159be7e6d"
    }]
    graphApi = {
      tenantId : "507015cf-5dd2-46ec-8df0-c567c092cd74",
      clientId : "da1213a1-0027-4673-bee3-d91f9b6294bd",
    }
    apiCustomDomain = {
      hostName = "api-waterschaplimburg.iprox-open.nl",
      provider = "adn-afd"
    }
    pmsCustomDomain = {
      hostName = "beheer-waterschaplimburg.iprox-open.nl",
      provider = "adn-afd"
    }
    portalCustomDomain = {
      hostName = "open.waterschaplimburg.nl",
      provider = "adn-afd"
    }
    frontdoorEndpoint = "one"
    apiIpAddresses    = []
    pmsIpAddresses    = []
    portalIpAddresses = []
    corsUrls          = ["https://waterschaplimburg.sharepoint.com", "https://open.waterschaplimburg.nl"]
  },
  {
    name           = "waterschap-limburg-test"
    deploymentRing = 1
    shortname      = "wsltest"
    plan           = "one"
    search         = "01"
    elasticpool    = "pool01"
    azureAd = [
      {
        id          = "azure-ad"
        name        = "Microsoft Entra ID waterschap Limburg test"
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "d1b8f26e-ed15-403f-b66a-f99e4881a620"
        clientId    = "iprox.open"
        pmsClientId = "27cfc069-ccff-415e-98d5-2812ebf1e24c"
      },
      {
        id          = "azure-ad-iprox"
        name        = "iprox.support"
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "1036265b-2bd7-4019-a418-1b3651df7347"
        clientId    = "iprox.open.support"
        pmsClientId = "cb6a3cee-63e4-4a5b-94fb-582159be7e6d"
      },
      {
        id          = "azure-ad-test"
        name        = "testinfoprojects"
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "ee21013a-99e5-4c19-a55a-72e16cc0e4fc"
        clientId    = "iprox.open"
        pmsClientId = "5ecda95f-c878-42a3-8f77-897f632c6ad2"
    }]
    graphApi = {
      tenantId = "d1b8f26e-ed15-403f-b66a-f99e4881a620"
      clientId = "1a36d733-8bff-4b98-9caf-902bd15c4e8a"
    }
    apiCustomDomain = {
      hostName = "api-waterschaplimburg-test.iprox-open.nl",
      provider = "adn-afd"
    }
    pmsCustomDomain = {
      hostName = "beheer-waterschaplimburg-test.iprox-open.nl",
      provider = "adn-afd"
    }
    portalCustomDomain = {
      hostName = "portaal-waterschaplimburg-test.iprox-open.nl",
      provider = "adn-afd"
    }
    frontdoorEndpoint = "one"
    apiIpAddresses    = []
    pmsIpAddresses    = []
    portalIpAddresses = []
    corsUrls          = ["https://testinfoprojects.sharepoint.com", "https://waterschaplimburgtest.sharepoint.com"]
  },
  {
    name           = "brabant"
    deploymentRing = 2
    shortname      = "pnb"
    plan           = "02"
    search         = "01"
    elasticpool    = "pool01"
    azureAd = [
      {
        id          = "azure-ad"
        name        = "Microsoft Entra ID Brabant"
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "d2aff5f9-8c21-47f2-88f3-08ac4fda56f5"
        clientId    = "iprox.open"
        pmsClientId = "8a958005-780f-414a-932d-1e5dcbdd2483"
      },
      {
        id          = "azure-ad-iprox"
        name        = "iprox.support"
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "1036265b-2bd7-4019-a418-1b3651df7347"
        clientId    = "iprox.open.support"
        pmsClientId = "cb6a3cee-63e4-4a5b-94fb-582159be7e6d"
      }
    ]
    apiCustomDomain = {
      hostName = "api-brabant.iprox-open.nl",
      provider = "adn-afd"
    }
    pmsCustomDomain = {
      hostName = "beheer-brabant.iprox-open.nl",
      provider = "adn-afd"
    }
    portalCustomDomain = {
      hostName = "open.brabant.nl",
      provider = "adn-clf"
    }
    frontdoorEndpoint = "one"
    apiIpAddresses    = []
    pmsIpAddresses    = []
    portalIpAddresses = []
    corsUrls          = []
  },
  {
    name           = "Provincie Drenthe"
    deploymentRing = 1
    shortname      = "pdr"
    plan           = "one"
    search         = "01"
    elasticpool    = "pool01"
    azureAd = [
      {
        id          = "azure-ad"
        name        = "Microsoft Entra ID provincie Drenthe."
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "c79a514e-fddd-4604-a413-d82f1aa74b13"
        clientId    = "iprox.open"
        pmsClientId = "ff38e340-6870-412d-b8c4-59b617439188"
      },
      {
        id          = "azure-ad-iprox"
        name        = "iprox.support"
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "1036265b-2bd7-4019-a418-1b3651df7347"
        clientId    = "iprox.open.support"
        pmsClientId = "cb6a3cee-63e4-4a5b-94fb-582159be7e6d"
    }]
    apiCustomDomain = {
      hostName = "api-drenthe.iprox-open.nl",
      provider = "adn-afd"
    }
    pmsCustomDomain = {
      hostName = "beheer-drenthe.iprox-open.nl",
      provider = "adn-afd"
    }
    portalCustomDomain = {
      hostName = "open.provincie.drenthe.nl",
      provider = "adn-clf"
    }
    frontdoorEndpoint = "one"
    apiIpAddresses    = []
    pmsIpAddresses    = []
    portalIpAddresses = []
    corsUrls          = []
  },
  {
    name           = "Veiligheidsregio Midden- en West-Brabant"
    deploymentRing = 1
    shortname      = "vmb"
    plan           = "one"
    search         = "01"
    elasticpool    = "pool01"
    azureAd = [
      {
        id          = "azure-ad"
        name        = "Microsoft Entra ID Veiligheidsregio Midden- en West-Brabant"
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "20316ca8-6512-41f7-9734-485fb756949b"
        clientId    = "iprox.open"
        pmsClientId = "dab172f9-c27b-4415-8285-1de39a7f278b"
      },
      {
        id          = "azure-ad-iprox"
        name        = "iprox.support"
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "1036265b-2bd7-4019-a418-1b3651df7347"
        clientId    = "iprox.open.support"
        pmsClientId = "cb6a3cee-63e4-4a5b-94fb-582159be7e6d"
    }]
    apiCustomDomain = {
      hostName = "api-vrmwb.iprox-open.nl",
      provider = "adn-afd"
    }
    pmsCustomDomain = {
      hostName = "beheer-vrmwb.iprox-open.nl",
      provider = "adn-afd"
    }
    portalCustomDomain = {
      hostName = "open.vrmwb.nl",
      provider = "adn-afd"
    }
    frontdoorEndpoint = "one"
    apiIpAddresses    = []
    pmsIpAddresses    = []
    portalIpAddresses = []
    corsUrls          = []
  },
  {
    name           = "Omgevingsdienst IJmond"
    deploymentRing = 2
    shortname      = "odij"
    plan           = "02"
    search         = "01"
    elasticpool    = "pool01"
    azureAd = [
      {
        id          = "azure-ad"
        name        = "Microsoft Entra ID OD IJmond"
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "801f46b5-c78e-4aac-b4e3-a86ac1626b29"
        clientId    = "iprox.open"
        pmsClientId = "623e4272-b8c3-49cf-8db9-9c1cd6d2fa51"
      },
      {
        id          = "azure-ad-iprox"
        name        = "iprox.support"
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "1036265b-2bd7-4019-a418-1b3651df7347"
        clientId    = "iprox.open.support"
        pmsClientId = "cb6a3cee-63e4-4a5b-94fb-582159be7e6d"
    }]
    apiCustomDomain = {
      hostName = "api-odijmond.iprox-open.nl",
      provider = "adn-afd"
    }
    pmsCustomDomain = {
      hostName = "beheer-odijmond.iprox-open.nl",
      provider = "adn-afd"
    }
    portalCustomDomain = {
      hostName = "portaal-odijmond.iprox-open.nl",
      provider = "adn-afd"
    }
    frontdoorEndpoint = "one"
    apiIpAddresses    = []
    pmsIpAddresses    = []
    portalIpAddresses = []
    corsUrls          = []
  },
  {
    name           = "Veiligheidsregio Drenthe"
    deploymentRing = 1
    shortname      = "vrd"
    plan           = "one"
    search         = "01"
    elasticpool    = "pool01"
    azureAd = [
      {
        id          = "azure-ad"
        name        = "Microsoft Entra ID iprox."
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "1036265b-2bd7-4019-a418-1b3651df7347"
        clientId    = "iprox.open.dev"
        pmsClientId = "da29d267-2430-4b27-ac68-2cd5a6aa5df0"
      },
      {
        id          = "azure-ad-iprox"
        name        = "iprox.support"
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "1036265b-2bd7-4019-a418-1b3651df7347"
        clientId    = "iprox.open.support"
        pmsClientId = "cb6a3cee-63e4-4a5b-94fb-582159be7e6d"
    }]
    apiCustomDomain = {
      hostName = "api-vrd.iprox-open.nl"
      provider = "adn-afd"
    }
    pmsCustomDomain = {
      hostName = "beheer-vrd.iprox-open.nl"
      provider = "adn-afd"
    }
    portalCustomDomain = {
      hostName = "portaal-vrd.iprox-open.nl"
      provider = "adn-afd"
    }
    frontdoorEndpoint = "one"
    apiIpAddresses    = []
    pmsIpAddresses    = []
    portalIpAddresses = []
    corsUrls          = []
  },
  {
    name           = "GGD Drenthe"
    deploymentRing = 1
    shortname      = "ggdd"
    plan           = "one"
    search         = "01"
    elasticpool    = "pool01"
    azureAd = [
      {
        id          = "azure-ad"
        name        = "Microsoft Entra ID iprox."
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "1036265b-2bd7-4019-a418-1b3651df7347"
        clientId    = "iprox.open.dev"
        pmsClientId = "da29d267-2430-4b27-ac68-2cd5a6aa5df0"
      },
      {
        id          = "azure-ad-iprox"
        name        = "iprox.support"
        instance    = "https://login.microsoftonline.com/"
        tenantId    = "1036265b-2bd7-4019-a418-1b3651df7347"
        clientId    = "iprox.open.support"
        pmsClientId = "cb6a3cee-63e4-4a5b-94fb-582159be7e6d"
    }]
    apiCustomDomain = {
      hostName = "api-ggddrenthe.iprox-open.nl"
      provider = "adn-afd"
    }
    pmsCustomDomain = {
      hostName = "beheer-ggddrenthe.iprox-open.nl"
      provider = "adn-afd"
    }
    portalCustomDomain = {
      hostName = "portaal-ggddrenthe.iprox-open.nl"
      provider = "adn-afd"
    }
    frontdoorEndpoint = "one"
    apiIpAddresses    = []
    pmsIpAddresses    = []
    portalIpAddresses = []
    corsUrls          = []
  }
]

productName       = "iprox-open"
resourceGroupName = "iprox-open-tenants"

tenantSecretsKeyVaultName = "kv-tenant-secrets"
aadClientAppSecretPrefix  = "aad-client-secret"

sqlDatabaseAbbreviation         = "sqldb"
appServiceAbbreviation          = "app"
applicationInsightsAbbreviation = "ai"
keyVaultAbbreviation            = "kv"

# Environment
location = "westeurope" # Location for all resources

# Sql params
sqlBackupStorageRedundancy = "Local"
sqlMaxSizeGigabytes        = 250
sqlSkuName                 = "S0"

# Storage
storageContainerName = "container"

# AppService params
apiDotnetVersion  = "8.0"
pmsNodeVersion    = "20-lts"
portalNodeVersion = "20-lts"

pmsAppServiceSuffix    = "pms"
portalAppServiceSuffix = "portal"

# App settings
aspNetCoreEnvironment = "Production" # Development for swagger/cors
httpsPort             = "443"        # Need this for IIS to forward to https
# Azure
tenantId = "1036265b-2bd7-4019-a418-1b3651df7347"
# File filters
fileFilterSettingsWhitelist = [
  "*.html",
  "*.htm",
  "*.svg",
  "*.txt",
  "*.csv",
  "*.xls",
  "*.xlsx",
  "*.doc",
  "*.docx",
  "*.pdf",
  "*.zip",
  "*.gif",
  "*.jpg",
  "*.jpeg",
  "*.png",
  "*.ppt",
  "*.pptx",
  "*.pps",
  "*.odt",
  "*.ods",
  "*.odg",
  "*.odp",
  "*.db"
]
fileFilterSettingsBlacklist      = ["*thumbs.db"]
fileFilterSettingsImageWhitelist = [".png", ".jpg", ".jpeg", ".webp", ".svg", ".gif", ".ico"]
fileFilterSettingsFontWhitelist  = ["*.ttf", "*.otf", "*.woff", "*.woff2", "*.eot", "*.svg"]
# Html Sanitization
htmlSanitizerSettingsAllowedTags = [
  "a",
  "i",
  "b",
  "u",
  "p",
  "ul",
  "li",
  "div",
  "table",
  "td",
  "tr",
  "th",
  "h2",
  "h3",
  "h4",
  "h5",
  "br"
]
htmlSanitizerSettingsAllowedAttributes = ["href", "title"]
htmlSanitizerSettingsAllowedClasses = [
  "ltr",
  "rtl",
  "editor-placeholder",
  "editor-paragraph",
  "editor-quote",
  "editor-heading-h1",
  "editor-heading-h2",
  "editor-heading-h3",
  "editor-heading-h4",
  "editor-heading-h5",
  "editor-nested-listitem",
  "editor-list-ol",
  "editor-list-ul",
  "editor-listitem",
  "editor-image",
  "editor-link",
  "editor-text-bold",
  "editor-text-italic",
  "editor-text-overflowed",
  "editor-text-underline",
  "editor-superlink",
  "bg-superlink-background",
"text-superlink-text"]
htmlSanitizerSettingsBlockedTags = ["script", "style"]
# Logging
serilogUsing = [
  "Serilog.Sinks.Console",
  "Serilog.Sinks.ApplicationInsights",
  "Serilog.Sinks.Slack",
  "Serilog.Enrichers.ClientInfo",
  "Serilog.Enrichers.Environment"
]
serilogMinimumLevelDefault           = "Information"
serilogMinimumLevelOverrideMicrosoft = "Warning"
serilogMinimumLevelOverrideSystem    = "Warning"
serilogEnrich = [
  {
    "Name" : "FromLogContext"
  },
  {
    "Name" : "WithClientIp"
  },
  {
    "Name" : "WithMachineName"
  },
  {
    "Name" : "WithCorrelationId"
  },
  {
    "Name" : "WithRequestHeader",
    "Args" : {
      "HeaderName" : "User-Agent"
    }
  },
  {
    "Name" : "WithRequestHeader",
    "Args" : {
      "HeaderName" : "Connection"
    }
  }
]
serilogWriteTo = [
  {
    "Name" : "Console"
  },
  {
    "Name" : "ApplicationInsights",
    "Args" : {
      "TelemetryConverter" : "Serilog.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"
    }
  },
  {
    "Name" : "Slack",
    "Args" : {
      "WebHookUrl" : "*******************************************************************************",
      "RestrictedToMinimumLevel" : "Error"
    }
  }
]

# Queue
azureQueueStorageName = "iproxopentenantsqueue"
# Cognitive Search
azureCognitiveSearchIndexPrefix = "index"
azureCognitiveSearchHighlightFields = [
  "dossier_title",
  "content",
  "node_name",
  "dossier_summary",
  "dynamic_fields/author",
  "dynamic_fields/documentnumber",
  "dynamic_fields/annotation",
  "dynamic_fields/publisher",
  "dynamic_fields/content",
  "dynamic_fields/dossiercontact"
]
azureCognitiveSearchHighlightPreTag  = "<mark class='search-highlight'>"
azureCognitiveSearchHighlightPostTag = "</mark>"

#Application Insights
applicationInsightsAgentExtensionVersion = "~2"

# Connection Strings
iproxOpenSqlDbName = "IproxOpenSqlDb"
iproxOpenSqlDbType = "SQLServer"

# PMS
nextAuthSecret = "ebb432a48ce1aaad95aefbb704386d02"

downloadSettings = {
  "timeLimit" : "1.00:00:00",
  "profiles" : [
    {
      "name" : "PublicProfile",
      "speedLimits" : [
        {
          "threshold" : "1GB",
          "speed" : "512Kbps"
        },
        {
          "threshold" : "5GB",
          "speed" : "256Kbps"
        }
      ]
    },
    {
      "name" : "PrivateProfile",
      "speedLimits" : [
        {
          "threshold" : "1GB",
          "speed" : "512Kbps"
        },
        {
          "threshold" : "5GB",
          "speed" : "256Kbps"
        }
      ]
    },
    {
      "name" : "CrawlerProfile",
      "speedLimits" : [
        {
          "threshold" : "1GB",
          "speed" : "512Kbps"
        },
        {
          "threshold" : "5GB",
          "speed" : "256Kbps"
        }
      ]
    }
  ],
  "unlimitedDownloadIps" : [
    ""
  ],
  "disallowedDownloadIps" : [
    ""
  ]
}
