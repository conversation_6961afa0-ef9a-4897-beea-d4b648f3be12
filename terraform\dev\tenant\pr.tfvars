productName       = "iprox-open"
resourceGroupName = "iprox.open"

tenantSecretsKeyVaultName = "kv-devenv-secrets"
aadClientAppSecretPrefix  = "aad-client-secret"

sqlDatabaseAbbreviation         = "sqldb"
appServiceAbbreviation          = "app"
applicationInsightsAbbreviation = "ai"
keyVaultAbbreviation            = "kv"

# Environment
location = "westeurope" # Location for all resources

# Sql params
sqlBackupStorageRedundancy = "Local"
sqlMaxSizeGigabytes        = 2 # TODO: Research what the max size value is here
sqlSkuName                 = "S0"

# Storage
storageContainerName = "container"

# AppService params
apiDotnetVersion  = "8.0"
pmsNodeVersion    = "20-lts"
portalNodeVersion = "20-lts"

pmsAppServiceSuffix    = "pms"
portalAppServiceSuffix = "portal"

# App settings
aspNetCoreEnvironment = "Development" # Development for swagger/cors
httpsPort             = "443"         # Need this for IIS to forward to https
# Azure
tenantId = "1036265b-2bd7-4019-a418-1b3651df7347"
# File filters
fileFilterSettingsWhitelist = [
  "*.html",
  "*.htm",
  "*.svg",
  "*.txt",
  "*.csv",
  "*.xls",
  "*.xlsx",
  "*.doc",
  "*.docx",
  "*.pdf",
  "*.zip",
  "*.gif",
  "*.jpg",
  "*.jpeg",
  "*.png",
  "*.ppt",
  "*.pptx",
  "*.pps",
  "*.odt",
  "*.ods",
  "*.odg",
  "*.odp",
  "*.db"
]
fileFilterSettingsBlacklist      = ["*thumbs.db"]
fileFilterSettingsImageWhitelist = [".png", ".jpg", ".jpeg", ".webp", ".svg", ".gif", ".ico"]
fileFilterSettingsFontWhitelist  = ["*.ttf", "*.otf", "*.woff", "*.woff2", "*.eot", "*.svg"]
# Html Sanitization
htmlSanitizerSettingsAllowedTags = [
  "a",
  "i",
  "b",
  "u",
  "p",
  "ul",
  "li",
  "div",
  "table",
  "td",
  "tr",
  "th",
  "h2",
  "h3",
  "h4",
  "h5",
  "br"
]
htmlSanitizerSettingsAllowedAttributes = ["href", "title"]
htmlSanitizerSettingsAllowedClasses = [
  "ltr",
  "rtl",
  "editor-placeholder",
  "editor-paragraph",
  "editor-quote",
  "editor-heading-h1",
  "editor-heading-h2",
  "editor-heading-h3",
  "editor-heading-h4",
  "editor-heading-h5",
  "editor-nested-listitem",
  "editor-list-ol",
  "editor-list-ul",
  "editor-listitem",
  "editor-image",
  "editor-link",
  "editor-text-bold",
  "editor-text-italic",
  "editor-text-overflowed",
  "editor-text-underline",
  "editor-superlink",
  "bg-superlink-background",
"text-superlink-text"]
htmlSanitizerSettingsBlockedTags = ["script", "style"]
# Logging
serilogUsing = [
  "Serilog.Sinks.Console",
  "Serilog.Sinks.ApplicationInsights",
  "Serilog.Sinks.Slack",
  "Serilog.Enrichers.ClientInfo",
  "Serilog.Enrichers.Environment"
]
serilogMinimumLevelDefault           = "Information"
serilogMinimumLevelOverrideMicrosoft = "Warning"
serilogMinimumLevelOverrideSystem    = "Warning"
serilogEnrich = [
  {
    "Name" : "FromLogContext"
  },
  {
    "Name" : "WithClientIp"
  },
  {
    "Name" : "WithMachineName"
  },
  {
    "Name" : "WithCorrelationId"
  },
  {
    "Name" : "WithRequestHeader",
    "Args" : {
      "HeaderName" : "User-Agent"
    }
  },
  {
    "Name" : "WithRequestHeader",
    "Args" : {
      "HeaderName" : "Connection"
    }
  }
]
serilogWriteTo = [
  {
    "Name" : "Console"
  },
  {
    "Name" : "ApplicationInsights",
    "Args" : {
      "TelemetryConverter" : "Serilog.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"
    }
  },
  {
    "Name" : "Slack",
    "Args" : {
      "WebHookUrl" : "*******************************************************************************",
      "RestrictedToMinimumLevel" : "Error"
    }
  }
]

# Queue
azureQueueStorageName = "iproxopendevqueue"
# Cognitive Search
azureCognitiveSearchIndexPrefix = "index"
azureCognitiveSearchHighlightFields = [
  "dossier_title",
  "content",
  "node_name",
  "dossier_summary",
  "dynamic_fields/author",
  "dynamic_fields/documentnumber",
  "dynamic_fields/annotation",
  "dynamic_fields/publisher",
  "dynamic_fields/content",
  "dynamic_fields/dossiercontact"
]
azureCognitiveSearchHighlightPreTag  = "<mark class='search-highlight'>"
azureCognitiveSearchHighlightPostTag = "</mark>"

#Application Insights
applicationInsightsAgentExtensionVersion = "~2"

# Connection Strings
iproxOpenSqlDbName = "IproxOpenSqlDb"
iproxOpenSqlDbType = "SQLServer"

# PMS
nextAuthSecret = "ebb432a48ce1aaad95aefbb704386d02"

downloadSettings = {
  "timeLimit" : "0.00:10:00",
  "profiles" : [
    {
      "name" : "PublicProfile",
      "speedLimits" : [
        {
          "threshold" : "1GB",
          "speed" : "512Kbps"
        },
        {
          "threshold" : "5GB",
          "speed" : "256Kbps"
        }
      ]
    },
    {
      "name" : "PrivateProfile",
      "speedLimits" : [
        {
          "threshold" : "1GB",
          "speed" : "512Kbps"
        },
        {
          "threshold" : "5GB",
          "speed" : "256Kbps"
        }
      ]
    },
    {
      "name" : "CrawlerProfile",
      "speedLimits" : [
        {
          "threshold" : "1GB",
          "speed" : "512Kbps"
        },
        {
          "threshold" : "5GB",
          "speed" : "256Kbps"
        }
      ]
    }
  ],
  "unlimitedDownloadIps" : [
    ""
  ],
  "disallowedDownloadIps" : [
    ""
  ]
}
