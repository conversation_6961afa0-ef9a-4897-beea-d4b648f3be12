resource "azurerm_key_vault" "key-vaults" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  name                = "${var.key_vault_prefix}-${each.value.shortname}"
  location            = var.location
  resource_group_name = var.resource_group_name
  tenant_id           = var.tenant_id
  sku_name            = var.sku

  # network_acls {
  #   default_action = "Allow"
  #   bypass         = "AzureServices"
  # }

  tags = {
    "${var.tenant_alias_tag_name}"     = "${each.value.name}"
    "${var.tenant_shortname_tag_name}" = "${each.value.shortname}"
    "${var.product_tag_name}"          = "${var.product_name}"
    "${var.env_tag_name}"              = "${var.env}"
  }

  lifecycle {
    prevent_destroy = true
  }
}
