# Create a login and user in the SQL Server
# resource "random_password" "sql-admin-password" {
#   keepers = {
#     login_name = var.sql_admin_login
#   }
#   length  = 20
#   special = false
# }

resource "random_password" "sql-server-admin-password" {
  length  = 20
  special = false
}

resource "azurerm_mssql_server" "sql-server" {
  name                         = terraform.workspace == "tenants" ? var.name : "${var.name}-${terraform.workspace}"
  resource_group_name          = var.resource_group_name
  location                     = var.location
  version                      = var.sql_server_version
  administrator_login          = var.administrator_login_username
  administrator_login_password = random_password.sql-server-admin-password.result

  lifecycle {
    prevent_destroy = true
  }
}

resource "azurerm_mssql_firewall_rule" "azure-ip" {
  name             = "windows.azure"
  server_id        = azurerm_mssql_server.sql-server.id
  start_ip_address = "0.0.0.0"
  end_ip_address   = "0.0.0.0"
}

resource "azurerm_mssql_firewall_rule" "iprox-office-ip" {
  name             = "iprox.office"
  server_id        = azurerm_mssql_server.sql-server.id
  start_ip_address = "************"
  end_ip_address   = "************"
}