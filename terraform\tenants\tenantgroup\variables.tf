# Configuration
variable "tenantGroupsConfiguration" {
  type = list(object(
    {
      name = string
      sku  = string
  }))
}

variable "searchServiceGroupsConfiguration" {
  type = list(object(
    {
      name = string
      sku  = string
  }))
}

variable "frontdoorEndpointConfiguration" {
  type = list(object(
    {
      name = string
  }))
}

variable "sqlElasticPoolGroupsConfiguration" {
  type = list(object(
    {
      name     = string
      sku      = string
      capacity = string
      tier     = string
  }))
}

variable "productName" {
  type    = string
  default = "iprox-open"
}

variable "resourceGroupName" {
  type    = string
  default = "dev"
}

variable "env" {
  type    = string
  default = "production"
}

variable "tenantId" {
  description = "Azure tenant ID"
  type        = string
}

# Abbreviations
variable "appServicePlanAbbreviation" {
  type    = string
  default = "asp"
}

variable "searchServiceAbbreviation" {
  type    = string
  default = "srch"
}

variable "sqlElasticPoolAbbreviation" {
  type    = string
  default = "sqlep"
}

# App Service
variable "appServicePlanOsType" {
  type    = string
  default = "Linux"
}

# Environment

variable "location" {
  type    = string
  default = "westeurope"
}


// Tags
variable "tenantGroupTagName" {
  type        = string
  description = "Tenant group tag name"
  default     = "tenant-group"
}

variable "frontDoorEndpointPrefix" {
  type    = string
  default = "afd-e"
}

