data "azurerm_storage_account_blob_container_sas" "storage-container-sass" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  connection_string = var.connection_string
  container_name    = lookup(var.tenant_storage_containers, "${each.value.name}").name
  https_only        = var.https_only

  start  = var.start_date
  expiry = var.expiry_date

  permissions {
    read   = var.read_permission
    add    = var.add_permission
    create = var.create_permission
    write  = var.write_permission
    delete = var.delete_permission
    list   = var.list_permission
  }
}