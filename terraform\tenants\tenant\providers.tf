terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 4.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.7.0"
    }
  }

  backend "azurerm" {
    resource_group_name  = "iprox.open"
    storage_account_name = "iproxopen"
    container_name       = "iprox-open-tfstate"
    key                  = "iprox-open-tenants-tenant.tfstate"
  }

  required_version = ">= 1.6.0"
}

provider "azurerm" {
  features {}
  subscription_id = "e3cff265-5d46-41fb-93f8-c3757a37d57a"
}
