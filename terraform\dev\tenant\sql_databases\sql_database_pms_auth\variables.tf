variable "tenants_configuration" {
  type = list(object(
    {
      name           = string
      deploymentRing = number
      shortname      = string
      plan           = string
      search         = string
      elasticpool    = string
      azureAd = list(object({
        id          = string
        name        = string
        instance    = string
        tenantId    = string
        clientId    = string
        pmsClientId = string
      }))
      apiCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      pmsCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      portalCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      frontdoorEndpoint = string
      apiIpAddresses    = list(string)
      pmsIpAddresses    = list(string)
      portalIpAddresses = list(string)
      corsUrls          = list(string)
  }))
  description = "this determines the names for all resources"
}

variable "tenants_sql_elastic_pools" {
  description = "Created sql elastic pools"
  type = map(object(
    {
      id = string
    }
  ))
}

variable "sql_database_prefix" {
  type = string
}

variable "sql_server_id" {
  type = string
}

variable "collation" {
  type = string
}

variable "max_size_gb" {
  type = string
}

variable "sku" {
  type = string
}

variable "read_scale" {
  type = bool
}

variable "zone_redundant" {
  type = bool
}

variable "geo_backup_enabled" {
  type = bool
}

variable "storage_account_type" {
  type = string
}

variable "tenant_alias_tag_name" {
  type = string
}

variable "tenant_shortname_tag_name" {
  type = string
}

variable "product_tag_name" {
  type = string
}

variable "env_tag_name" {
  type = string
}

variable "product_name" {
  type = string
}

variable "env" {
  type = string
}
