resource "azurerm_application_insights" "application-insights-apis" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  name                = "${var.application_insights_api_prefix}-${each.value.shortname}"
  location            = var.location
  resource_group_name = var.resource_group_name
  application_type    = var.application_type
  workspace_id        = var.workspace_id

  tags = {
    "${var.tenant_alias_tag_name}"     = "${each.value.name}"
    "${var.tenant_shortname_tag_name}" = "${each.value.shortname}"
    "${var.product_tag_name}"          = "${var.product_name}"
    "${var.env_tag_name}"              = "${var.env}"
  }
}