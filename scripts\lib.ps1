# PowerShell Library Functions
# Common utility functions for deployment scripts

$servicePrincipalId = $env:SERVICEPRINCIPALID
$servicePrincipalSecret = $env:SERVICEPRINCIPALSECRET
$tenantId = $env:TENANTID
$subscriptionId = $env:SUBSCRIPTIONID
$environment = $env:environment
$ringsToDeploy = $env:RINGSTODEPLOY

function Write-Info {
  param([string]$message)
  Write-Host $message -ForegroundColor Green
}

function Connect-Azure {
  param(
    [Parameter(Mandatory = $true)]
    [string]$servicePrincipalId,

    [Parameter(Mandatory = $true)]
    [string]$servicePrincipalSecret,

    [Parameter(Mandatory = $true)]
    [string]$tenantId,

    [Parameter(Mandatory = $true)]
    [string]$subscriptionId
  )

  Write-Info "Setting up Azure environment variables"
  $env:ARM_CLIENT_ID = $servicePrincipalId
  $env:ARM_CLIENT_SECRET = $servicePrincipalSecret
  $env:ARM_TENANT_ID = $tenantId
  $env:ARM_SUBSCRIPTION_ID = $subscriptionId

  Write-Info "Logging in to Azure"
  az login --service-principal -u $servicePrincipalId -p $servicePrincipalSecret -t $tenantId
  if ($LASTEXITCODE -ne 0) {
    throw "Azure login failed with exit code $LASTEXITCODE"
  }

  Write-Info "Setting Azure subscription: $subscriptionId"
  az account set --subscription $subscriptionId
  if ($LASTEXITCODE -ne 0) {
    throw "Failed to set Azure subscription with exit code $LASTEXITCODE"
  }

  Write-Info "Azure connection established successfully"
}

function Get-TerraformOutput {
  param(
    [Parameter(Mandatory = $true)]
    [string]$terraformPath,

    [Parameter(Mandatory = $true)]
    [string]$workspace
  )

  Write-Info 'Using Terraform output to generate extra release info'

  Push-Location $terraformPath

  try {
    Write-Info "Selecting workspace: $workspace"
    terraform workspace select $workspace
    if ($LASTEXITCODE -ne 0) {
      throw "Failed to select terraform workspace '$workspace' with exit code $LASTEXITCODE"
    }

    Write-Info "Getting terraform outputs"
    $tfOutput = terraform output -json
    if ($LASTEXITCODE -ne 0) {
      throw "Failed to get terraform output with exit code $LASTEXITCODE"
    }

    $tfOutputObject = $tfOutput | ConvertFrom-Json
    Write-Info "Successfully retrieved terraform outputs"

    return $tfOutputObject
  }
  catch {
    Write-Error "Failed to get terraform output: $_"
    throw
  }
  finally {
    Pop-Location
  }
}
