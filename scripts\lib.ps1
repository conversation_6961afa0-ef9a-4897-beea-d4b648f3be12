# PowerShell Library Functions
# Common utility functions for deployment scripts

function Write-Info {
  param([string]$message)
  Write-Host $message -ForegroundColor Green
}

function Invoke-TerraformDeploy {
  param(
    [Parameter(Mandatory = $true)]
    [string]$WorkingDirectory,

    [Parameter(Mandatory = $true)]
    [string]$Workspace,

    [Parameter(Mandatory = $true)]
    [string]$VarFile,

    [string]$AdditionalPlanArgs = "",

    [switch]$SkipApply
  )

  Write-Info "Starting Terraform deployment in: $WorkingDirectory"
  Write-Info "Workspace: $Workspace"
  Write-Info "Var file: $VarFile"

  # Change to working directory
  Push-Location $WorkingDirectory

  try {
    # Step 1: Terraform Init
    Write-Info "Running terraform init..."
    terraform init
    if ($LASTEXITCODE -ne 0) {
      throw "Terraform init failed with exit code $LASTEXITCODE"
    }

    # Step 2: Select workspace
    Write-Info "Selecting workspace: $Workspace"
    terraform workspace select $Workspace
    if ($LASTEXITCODE -ne 0) {
      throw "Terraform workspace select failed with exit code $LASTEXITCODE"
    }

    # Step 3: Terraform Plan
    $planFile = "$Workspace-tfplan"
    $planCommand = "terraform plan -var-file=`"$VarFile`" -out `"$planFile`""
    if ($AdditionalPlanArgs) {
      $planCommand += " $AdditionalPlanArgs"
    }

    Write-Info "Running terraform plan..."
    Write-Info "Command: $planCommand"
    Invoke-Expression $planCommand
    if ($LASTEXITCODE -ne 0) {
      throw "Terraform plan failed with exit code $LASTEXITCODE"
    }

    # Step 4: Terraform Apply (if not skipped)
    if (-not $SkipApply) {
      Write-Info "Running terraform apply..."
      terraform apply "$planFile"
      if ($LASTEXITCODE -ne 0) {
        throw "Terraform apply failed with exit code $LASTEXITCODE"
      }
      Write-Info "Terraform deployment completed successfully!"
    }
    else {
      Write-Info "Terraform apply skipped (plan-only mode)"
    }
  }
  catch {
    Write-Error "Terraform deployment failed: $_"
    throw
  }
  finally {
    # Return to original directory
    Pop-Location
  }
}
