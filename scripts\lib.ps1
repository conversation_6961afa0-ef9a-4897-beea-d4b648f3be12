# PowerShell Library Functions
# Common utility functions for deployment scripts

function Write-Info {
  param([string]$message)
  Write-Host $message -ForegroundColor Green
}

function Connect-Azure {
  param(
    [Parameter(Mandatory = $true)]
    [string]$servicePrincipalId,

    [Parameter(Mandatory = $true)]
    [string]$servicePrincipalSecret,

    [Parameter(Mandatory = $true)]
    [string]$tenantId,

    [Parameter(Mandatory = $true)]
    [string]$subscriptionId
  )

  Write-Info "Setting up Azure environment variables"
  $env:ARM_CLIENT_ID = $servicePrincipalId
  $env:ARM_CLIENT_SECRET = $servicePrincipalSecret
  $env:ARM_TENANT_ID = $tenantId
  $env:ARM_SUBSCRIPTION_ID = $subscriptionId

  Write-Info "Logging in to Azure"
  az login --service-principal -u $servicePrincipalId -p $servicePrincipalSecret -t $tenantId
  if ($LASTEXITCODE -ne 0) {
    throw "Azure login failed with exit code $LASTEXITCODE"
  }

  Write-Info "Setting Azure subscription: $subscriptionId"
  az account set --subscription $subscriptionId
  if ($LASTEXITCODE -ne 0) {
    throw "Failed to set Azure subscription with exit code $LASTEXITCODE"
  }

  Write-Info "Azure connection established successfully"
}
