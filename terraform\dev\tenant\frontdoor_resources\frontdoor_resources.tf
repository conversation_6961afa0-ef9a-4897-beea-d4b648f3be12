// API
resource "azurerm_cdn_frontdoor_origin_group" "frontdoor-origin-groups-api" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  name                     = "${var.frontdoor_origin_group_prefix}-${var.tenant_app_service_apis[each.value.name].name}"
  cdn_frontdoor_profile_id = var.frontdoor_id
  session_affinity_enabled = true

  load_balancing {
    sample_size                        = 4
    successful_samples_required        = 3
    additional_latency_in_milliseconds = 50
  }

  health_probe {
    path                = "/"
    request_type        = "HEAD"
    protocol            = "Https"
    interval_in_seconds = 100
  }
}

resource "azurerm_cdn_frontdoor_origin" "frontdoor-origins-api" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  name                          = "${var.frontdoor_origin_prefix}-${var.tenant_app_service_apis[each.value.name].name}"
  cdn_frontdoor_origin_group_id = azurerm_cdn_frontdoor_origin_group.frontdoor-origin-groups-api[each.key].id

  enabled                        = true
  host_name                      = var.tenant_app_service_apis[each.value.name].default_hostname
  http_port                      = 80
  https_port                     = 443
  origin_host_header             = var.tenant_app_service_apis[each.value.name].default_hostname
  priority                       = 1
  weight                         = 1000
  certificate_name_check_enabled = true

  depends_on = [azurerm_cdn_frontdoor_origin_group.frontdoor-origin-groups-api]
}
resource "azurerm_cdn_frontdoor_custom_domain" "frontdoor-custom-domain-api" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  name                     = "api-${each.value.shortname}"
  cdn_frontdoor_profile_id = var.frontdoor_id
  host_name                = each.value.apiCustomDomain.hostName

  tls {
    certificate_type = "ManagedCertificate"
  }
}

resource "azurerm_cdn_frontdoor_route" "frontdoor-routes-api" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant if tenant.apiCustomDomain.provider == "adn-afd" }

  name = "${var.frontdoor_route_prefix}-${var.tenant_app_service_apis[each.value.name].name}"

  cdn_frontdoor_endpoint_id     = var.tenantgroup_frontdoor_endpoint[each.value.frontdoorEndpoint].id
  cdn_frontdoor_origin_group_id = azurerm_cdn_frontdoor_origin_group.frontdoor-origin-groups-api[each.key].id
  cdn_frontdoor_origin_ids      = [azurerm_cdn_frontdoor_origin.frontdoor-origins-api[each.key].id]

  cdn_frontdoor_custom_domain_ids = each.value.apiCustomDomain.provider != "adn-awn" ? [azurerm_cdn_frontdoor_custom_domain.frontdoor-custom-domain-api[each.key].id] : []

  supported_protocols    = ["Http", "Https"]
  patterns_to_match      = ["/*"]
  forwarding_protocol    = "HttpsOnly"
  link_to_default_domain = false
  https_redirect_enabled = true

  depends_on = [azurerm_cdn_frontdoor_origin_group.frontdoor-origin-groups-api,
    azurerm_cdn_frontdoor_origin.frontdoor-origins-api,
    azurerm_cdn_frontdoor_custom_domain.frontdoor-custom-domain-api
  ]
}

// PMS
resource "azurerm_cdn_frontdoor_origin_group" "frontdoor-origin-groups-pms" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  name                     = "${var.frontdoor_origin_group_prefix}-${var.tenant_app_service_pmss[each.value.name].name}"
  cdn_frontdoor_profile_id = var.frontdoor_id
  session_affinity_enabled = true

  load_balancing {
    sample_size                        = 4
    successful_samples_required        = 3
    additional_latency_in_milliseconds = 50
  }

  health_probe {
    path                = "/"
    request_type        = "HEAD"
    protocol            = "Https"
    interval_in_seconds = 100
  }
}

resource "azurerm_cdn_frontdoor_origin" "frontdoor-origins-pms" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  name                          = "${var.frontdoor_origin_prefix}-${var.tenant_app_service_pmss[each.value.name].name}"
  cdn_frontdoor_origin_group_id = azurerm_cdn_frontdoor_origin_group.frontdoor-origin-groups-pms[each.key].id

  enabled                        = true
  host_name                      = var.tenant_app_service_pmss[each.value.name].default_hostname
  http_port                      = 80
  https_port                     = 443
  origin_host_header             = var.tenant_app_service_pmss[each.value.name].default_hostname
  priority                       = 1
  weight                         = 1000
  certificate_name_check_enabled = true

  depends_on = [azurerm_cdn_frontdoor_origin_group.frontdoor-origin-groups-pms]
}

resource "azurerm_cdn_frontdoor_custom_domain" "frontdoor-custom-domain-pms" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  name                     = "pms-${each.value.shortname}"
  cdn_frontdoor_profile_id = var.frontdoor_id
  host_name                = each.value.pmsCustomDomain.hostName

  tls {
    certificate_type = "ManagedCertificate"
  }
}

resource "azurerm_cdn_frontdoor_route" "frontdoor-routes-pms" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant if tenant.pmsCustomDomain.provider == "adn-afd" }

  name = "${var.frontdoor_route_prefix}-${var.tenant_app_service_pmss[each.value.name].name}"

  cdn_frontdoor_endpoint_id     = var.tenantgroup_frontdoor_endpoint[each.value.frontdoorEndpoint].id
  cdn_frontdoor_origin_group_id = azurerm_cdn_frontdoor_origin_group.frontdoor-origin-groups-pms[each.key].id
  cdn_frontdoor_origin_ids      = [azurerm_cdn_frontdoor_origin.frontdoor-origins-pms[each.key].id]

  cdn_frontdoor_custom_domain_ids = each.value.pmsCustomDomain.provider != "adn-awn" ? [azurerm_cdn_frontdoor_custom_domain.frontdoor-custom-domain-pms[each.key].id] : []

  supported_protocols    = ["Http", "Https"]
  patterns_to_match      = ["/*"]
  forwarding_protocol    = "HttpsOnly"
  link_to_default_domain = false
  https_redirect_enabled = true

  depends_on = [azurerm_cdn_frontdoor_origin_group.frontdoor-origin-groups-pms,
    azurerm_cdn_frontdoor_origin.frontdoor-origins-pms,
    azurerm_cdn_frontdoor_custom_domain.frontdoor-custom-domain-pms
  ]
}

// PORTAL
resource "azurerm_cdn_frontdoor_origin_group" "frontdoor-origin-groups-portal" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  name                     = "${var.frontdoor_origin_group_prefix}-${var.tenant_app_service_portals[each.value.name].name}"
  cdn_frontdoor_profile_id = var.frontdoor_id
  session_affinity_enabled = true

  load_balancing {
    sample_size                        = 4
    successful_samples_required        = 3
    additional_latency_in_milliseconds = 50
  }

  health_probe {
    path                = "/"
    request_type        = "HEAD"
    protocol            = "Https"
    interval_in_seconds = 100
  }
}

resource "azurerm_cdn_frontdoor_origin" "frontdoor-origins-portal" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  name                          = "${var.frontdoor_origin_prefix}-${var.tenant_app_service_portals[each.value.name].name}"
  cdn_frontdoor_origin_group_id = azurerm_cdn_frontdoor_origin_group.frontdoor-origin-groups-portal[each.key].id

  enabled                        = true
  host_name                      = var.tenant_app_service_portals[each.value.name].default_hostname
  http_port                      = 80
  https_port                     = 443
  origin_host_header             = var.tenant_app_service_portals[each.value.name].default_hostname
  priority                       = 1
  weight                         = 1000
  certificate_name_check_enabled = true

  depends_on = [azurerm_cdn_frontdoor_origin_group.frontdoor-origin-groups-portal]
}

resource "azurerm_cdn_frontdoor_custom_domain" "frontdoor-custom-domain-portal" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  name                     = "portal-${each.value.shortname}"
  cdn_frontdoor_profile_id = var.frontdoor_id
  host_name                = each.value.portalCustomDomain.hostName

  tls {
    certificate_type = "ManagedCertificate"
  }
}

resource "azurerm_cdn_frontdoor_route" "frontdoor-routes-portal" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant if tenant.portalCustomDomain.provider == "adn-afd" }

  name = "${var.frontdoor_route_prefix}-${var.tenant_app_service_portals[each.value.name].name}"

  cdn_frontdoor_endpoint_id     = var.tenantgroup_frontdoor_endpoint[each.value.frontdoorEndpoint].id
  cdn_frontdoor_origin_group_id = azurerm_cdn_frontdoor_origin_group.frontdoor-origin-groups-portal[each.key].id
  cdn_frontdoor_origin_ids      = [azurerm_cdn_frontdoor_origin.frontdoor-origins-portal[each.key].id]

  cdn_frontdoor_custom_domain_ids = each.value.portalCustomDomain.provider != "adn-awn" ? [azurerm_cdn_frontdoor_custom_domain.frontdoor-custom-domain-portal[each.key].id] : []

  supported_protocols    = ["Http", "Https"]
  patterns_to_match      = ["/*"]
  forwarding_protocol    = "HttpsOnly"
  link_to_default_domain = false
  https_redirect_enabled = true

  depends_on = [azurerm_cdn_frontdoor_origin_group.frontdoor-origin-groups-portal,
    azurerm_cdn_frontdoor_origin.frontdoor-origins-portal,
    azurerm_cdn_frontdoor_custom_domain.frontdoor-custom-domain-portal
  ]
}
