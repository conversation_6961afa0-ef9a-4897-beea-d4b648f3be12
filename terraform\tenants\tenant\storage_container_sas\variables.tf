variable "tenants_configuration" {
  type = list(object(
    {
      name           = string
      deploymentRing = number
      shortname      = string
      plan           = string
      search         = string
      elasticpool    = string
      azureAd = list(object({
        id          = string
        name        = string
        instance    = string
        tenantId    = string
        clientId    = string
        pmsClientId = string
      }))
      apiCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      pmsCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      portalCustomDomain = optional(object({
        hostName = string
        provider = string
      }))
      frontdoorEndpoint = string
      apiIpAddresses    = list(string)
      pmsIpAddresses    = list(string)
      portalIpAddresses = list(string)
      corsUrls          = list(string)
  }))
  description = "this determines the names for all resources"
}

variable "tenant_storage_containers" {
  description = "Map of Storage Containers to their tenants"
  type = map(object(
    {
      name = string
    }
  ))
}

variable "connection_string" {
  type = string
}

variable "https_only" {
  type = string
}

variable "start_date" {
  type = string
}

variable "expiry_date" {
  type = string
}

variable "read_permission" {
  type = string
}

variable "write_permission" {
  type = string
}

variable "delete_permission" {
  type = string
}

variable "list_permission" {
  type = string
}

variable "add_permission" {
  type = string
}

variable "create_permission" {
  type = string
}
