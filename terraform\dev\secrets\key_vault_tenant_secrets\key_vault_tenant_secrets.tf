data "azurerm_key_vault" "tenant-secrets-key-vault" {
  name                = var.tenant_secrets_key_vault_name
  resource_group_name = var.resource_group_name
}

locals {
  tenant_secrets = flatten([
    for tenant in var.tenants_configuration : [
      for azureAd in tenant.azureAd : {
        name      = tenant.name
        shortname = tenant.shortname
        id        = substr(azureAd.id, 8, -1)
      }
    ]
  ])
  graph_secrets = [
    for tenant in var.tenants_configuration : {
      name      = tenant.name
      shortname = tenant.shortname
    } if tenant.graphApi != null
  ]
}

data "azurerm_key_vault_secret" "tenant-secrets" {
  for_each = { for ts in local.tenant_secrets : "${ts.name}${ts.id}" => ts }

  name         = can(regex("^pr[0-9]+$", each.value.name)) ? "${var.aad_client_app_secret_prefix}-dev${each.value.id}" : "${var.aad_client_app_secret_prefix}-${each.value.shortname}${each.value.id}"
  key_vault_id = data.azurerm_key_vault.tenant-secrets-key-vault.id
}

data "azurerm_key_vault_secret" "tenant-graph-api-secrets" {
  for_each = { for ts in local.graph_secrets : "${ts.name}" => ts }

  name         = can(regex("^pr[0-9]+$", each.value.name)) ? "${var.graph_api_client_secret_prefix}-dev" : "${var.graph_api_client_secret_prefix}-${each.value.shortname}"
  key_vault_id = data.azurerm_key_vault.tenant-secrets-key-vault.id
}
