# MUST PASS IN VARS
variable "ringsToDeploy" {
  type = list(number)
}

variable "tenantsConfiguration" {
  type = list(object(
    {
      name           = string # Full name of the tenant
      deploymentRing = number # Ring in which this tenant should be deployed
      shortname      = string # Acronym for the client, for resource naming
      plan           = string # Search service group this tenant is allocated to
      search         = string # App service plan this tenant is allocated to
      elasticpool    = string # Elastic pool this tenant is allocated to
      azureAd = list(object({
        id          = string # ID to be used for retrieving client secret from key vault
        name        = string # Name of the Azure AD client (display only)
        instance    = string # "https://login.microsoftonline.com/" for Azure
        tenantId    = string # The GUID value of the tenant ID that this application is registered in
        clientId    = string # The text value of the client ID of the application
        pmsClientId = string # The GUID value of the client ID of the application
      }))                    # Azure AD client configurations for this tenant
      graphApi = optional(object({
        tenantId = string # The GUID value of the tenant ID that this application is registered in
        clientId = string # The GUID value of the client ID of the application
      }))                 # Graph API client configurations for this tenant
      apiCustomDomain = optional(object({
        hostName = string # Custom Domain for the API
        provider = string # provider for the custom domain for the API
      }))
      pmsCustomDomain = optional(object({
        hostName = string # Custom Domain for the pms
        provider = string # provider for the custom domain for the pms
      }))
      portalCustomDomain = optional(object({
        hostName = string # Custom Domain for the portal
        provider = string # provider for the custom domain for the portal
      }))
      frontdoorEndpoint = string       # Frontdoor endpoint this tenant is allocated to
      apiIpAddresses    = list(string) # List of IP addresses to restrict access to the API
      pmsIpAddresses    = list(string) # List of IP addresses to restrict access to the PMS
      portalIpAddresses = list(string) # List of IP addresses to restrict access to the Portal
      corsUrls          = list(string) # List of URLs to allow CORS
  }))
  description = "Tenant configurations"
}

variable "tenantSecretsKeyVaultName" {
  type    = string
  default = "kv-devenv-secrets"
}

variable "aadClientAppSecretPrefix" {
  type    = string
  default = "aad-client-secret"
}

variable "graphApiClientSecretPrefix" {
  type    = string
  default = "graph-api-client-secret"
}

variable "productName" {
  type    = string
  default = "iprox-open"
}

variable "resourceGroupName" {
  type    = string
  default = "dev"
}

variable "env" {
  type    = string
  default = "production"
}

# Abbreviations
variable "sqlServerAbbreviation" {
  type    = string
  default = "sql"
}

variable "sqlDatabaseAbbreviation" {
  type    = string
  default = "sqldb"
}

variable "appServicePlanAbbreviation" {
  type    = string
  default = "asp"
}

variable "appServiceAbbreviation" {
  type    = string
  default = "app"
}

variable "applicationInsightsAbbreviation" {
  type    = string
  default = "ai"
}

variable "logAnalyticsWorkspaceAbbreviation" {
  type    = string
  default = "log"
}

variable "searchServiceAbbreviation" {
  type    = string
  default = "srch"
}

variable "frontdoorAbbreviation" {
  type    = string
  default = "afd"
}

variable "keyVaultAbbreviation" {
  type    = string
  default = "kv"
}

variable "storageAccountAbbreviation" {
  type    = string
  default = "st"
}

variable "sqlElasticPoolAbbreviation" {
  type    = string
  default = "sqlep"
}

# Environment

variable "location" {
  type    = string
  default = "westeurope"
}

# Sql
variable "sqlServerVersion" {
  type    = string
  default = "12.0"
}

variable "sqlBackupStorageRedundancy" {
  type    = string
  default = "Local"
}

variable "sqlMaxSizeGigabytes" {
  type    = number
  default = 250
}

variable "sqlSkuName" {
  type    = string
  default = "S0"
}

#Frontdoor
variable "frontDoorSkuName" {
  type        = string
  description = "The SKU for the Front Door profile. Possible values include: Standard_AzureFrontDoor, Premium_AzureFrontDoor"
  default     = "Standard_AzureFrontDoor"
  validation {
    condition     = contains(["Standard_AzureFrontDoor", "Premium_AzureFrontDoor"], var.frontDoorSkuName)
    error_message = "The SKU value must be one of the following: Standard_AzureFrontDoor, Premium_AzureFrontDoor."
  }
}

# Storage
variable "storageAccountName" {
  type    = string
  default = "iproxopenstorageaccount"
}

variable "storageContainerName" {
  type    = string
  default = "container"
}

# App Service
variable "appServicePlanOsType" {
  type    = string
  default = "Linux"
}

variable "apiDotnetVersion" {
  type = string
}

variable "pmsNodeVersion" {
  type = string
}

variable "portalNodeVersion" {
  type = string
}

variable "pmsAppServiceSuffix" {
  type = string
}

variable "portalAppServiceSuffix" {
  type = string
}

# Application Insights
variable "logAnalyticsWorkspaceSku" {
  type    = string
  default = "PerGB2018"
}

variable "logAnalyticsWorkspaceDailyQuotaDb" {
  type    = string
  default = "0.023"
}

variable "logAnalyticsWorkspacRetentionInDays" {
  type    = number
  default = 120
}

# AppSettings
variable "aspNetCoreEnvironment" {
  description = "Development for swagger/cors"
  type        = string
}

variable "httpsPort" {
  description = "Need this for IIS to forward to https"
  type        = string
}

variable "tenantId" {
  description = "Azure tenant ID"
  type        = string
}

variable "fileFilterSettingsWhitelist" {
  description = "File filter settings whitelist"
  type        = list(string)
}

variable "fileFilterSettingsBlacklist" {
  description = "File filter settings blacklist"
  type        = list(string)
}

variable "fileFilterSettingsImageWhitelist" {
  description = "File filter settings image whitelist"
  type        = list(string)
}

variable "fileFilterSettingsFontWhitelist" {
  description = "File filter settings font whitelist"
  type        = list(string)
}

variable "htmlSanitizerSettingsAllowedTags" {
  description = "HTML sanitizer settings allowed tags"
  type        = list(string)
}

variable "htmlSanitizerSettingsAllowedAttributes" {
  description = "HTML sanitizer settings allowed attributes"
  type        = list(string)
}

variable "htmlSanitizerSettingsAllowedClasses" {
  description = "HTML sanitizer settings allowed classes"
  type        = list(string)
}

variable "htmlSanitizerSettingsBlockedTags" {
  description = "HTML sanitizer settings blocked tags"
  type        = list(string)
}

variable "serilogUsing" {
  description = "Serilog using"
  type        = list(string)
}

variable "serilogMinimumLevelDefault" {
  description = "Serilog minimum level default"
  type        = string
}

variable "serilogMinimumLevelOverrideMicrosoft" {
  description = "Serilog minimum level override Microsoft"
  type        = string
}

variable "serilogMinimumLevelOverrideSystem" {
  description = "Serilog minimum level override System"
  type        = string
}

variable "serilogEnrich" {
  description = "Serilog enrich"
  type = list(object(
    {
      Name = string
      Args = optional(object(
        {
          HeaderName = optional(string)
        }
      ))
    },
  ))
}

variable "serilogWriteTo" {
  description = "Serilog write to"
  type = list(object(
    {
      Name = string
      Args = optional(object(
        {
          TelemetryConverter       = optional(string)
          WebHookUrl               = optional(string)
          RestrictedToMinimumLevel = optional(string)
        }
      ))
    },
  ))
}

variable "azureQueueStorageName" {
  description = "Azure Queue Name"
  type        = string
}

variable "azureCognitiveSearchIndexPrefix" {
  description = "Azure Cognitive Search Index Prefix"
  type        = string
}

variable "azureCognitiveSearchHighlightFields" {
  description = "Azure Cognitive Search Highlight Fields"
  type        = list(string)
}

variable "azureCognitiveSearchHighlightPreTag" {
  description = "Azure Cognitive Search Highlight Pre Tag"
  type        = string
}

variable "azureCognitiveSearchHighlightPostTag" {
  description = "Azure Cognitive Search Highlight Post Tag"
  type        = string
}

variable "applicationInsightsAgentExtensionVersion" {
  description = "Application Insights Agent Extension Version"
  type        = string
}

variable "iproxOpenSqlDbName" {
  description = "Iprox Open SQL DB Name"
  type        = string
}

variable "iproxOpenSqlDbType" {
  description = "Iprox Open SQL DB Type"
  type        = string
}

variable "nextAuthSecret" {
  description = "Next Auth Secret"
  type        = string
  sensitive   = true
}

// Tags
variable "tenantGroupTagName" {
  type        = string
  description = "Tenant group tag name"
  default     = "tenant-group"
}

variable "tenantAliasTagName" {
  type        = string
  description = "Tenant alias tag name"
  default     = "tenant-alias"
}

variable "tenantShortnameTagName" {
  type        = string
  description = "Tenant alias tag name"
  default     = "tenant-shortname"
}

variable "productTagName" {
  type        = string
  description = "Product tag name"
  default     = "product"
}

variable "envTagName" {
  type        = string
  description = "Environment tag name"
  default     = "env"
}

// Frontdoor Prefixes
variable "frontDoorOriginGroupPrefix" {
  type    = string
  default = "afd-og"
}

variable "frontDoorOriginPrefix" {
  type    = string
  default = "afd-o"
}

variable "frontDoorEndpointPrefix" {
  type    = string
  default = "afd-e"
}

variable "frontDoorRoutePrefix" {
  type    = string
  default = "afd-r"
}

variable "downloadSettings" {
  description = "Download profile settings"
  type = object({
    timeLimit = string,
    profiles = list(object({
      name = string,
      speedLimits = list(object({
        threshold = string,
        speed     = string
      }))
    })),
    unlimitedDownloadIps  = list(string),
    disallowedDownloadIps = list(string),
  })
}

// Key Vault Permissions
variable "keyVaultKeyPermissionsFull" {
  type        = list(string)
  description = "List of full key permissions, must be one or more from the following: Backup, Create, Decrypt, Delete, Encrypt, Get, Import, List, Purge, Recover, Restore, Sign, UnwrapKey, Update, Verify and WrapKey."
  default     = ["Backup", "Create", "Decrypt", "Delete", "Encrypt", "Get", "Import", "List", "Purge", "Recover", "Restore", "Sign", "UnwrapKey", "Update", "Verify", "WrapKey"]
}

variable "keyVaultSecretPermissionsFull" {
  type        = list(string)
  description = "List of full secret permissions, must be one or more from the following: Backup, Delete, Get, List, Purge, Recover, Restore and Set"
  default     = ["Backup", "Delete", "Get", "List", "Purge", "Recover", "Restore", "Set"]
}

variable "keyVaultCertificatePermissionsFull" {
  type        = list(string)
  description = "List of full certificate permissions, must be one or more from the following: Backup, Create, Delete, DeleteIssuers, Get, GetIssuers, Import, List, ListIssuers, ManageContacts, ManageIssuers, Purge, Recover, Restore, SetIssuers and Update"
  default     = ["Create", "Delete", "DeleteIssuers", "Get", "GetIssuers", "Import", "List", "ListIssuers", "ManageContacts", "ManageIssuers", "Purge", "Recover", "SetIssuers", "Update", "Backup", "Restore"]
}

variable "keyVaultStoragePermissionsFull" {
  type        = list(string)
  description = "List of full storage permissions, must be one or more from the following: Backup, Delete, DeleteSAS, Get, GetSAS, List, ListSAS, Purge, Recover, RegenerateKey, Restore, Set, SetSAS and Update"
  default     = ["Backup", "Delete", "DeleteSAS", "Get", "GetSAS", "List", "ListSAS", "Purge", "Recover", "RegenerateKey", "Restore", "Set", "SetSAS", "Update"]
}

// Download token
variable "downloadTokenIssuer" {
  type    = string
  default = "iprox.open"
}

variable "downloadTokenAudience" {
  type    = string
  default = "iprox.open"
}

variable "downloadTokenSecretKey" {
  type    = string
  default = "zKQQqJ0hfnmEGTl0nh6Yyg4pWk1HQf6j"
}
