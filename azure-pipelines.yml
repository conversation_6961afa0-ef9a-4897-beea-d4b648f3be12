trigger:
  - main

pool:
  vmImage: "windows-latest"

variables:
  solution: "**/*.sln"
  buildPlatform: "Any CPU"
  buildConfiguration: "Release"

steps:
  - task: PublishPipelineArtifact@1
    displayName: Publish Scripts artifact
    inputs:
      targetPath: "$(Build.SourcesDirectory)/scripts"
      artifactName: "scripts"
    condition: and(succeeded(), in(variables['Build.Reason'], 'IndividualCI', 'BatchedCI', 'Manual'))

  - task: PublishPipelineArtifact@1
    displayName: Publish Terraform artifact
    inputs:
      targetPath: "$(Build.SourcesDirectory)/terraform"
      artifactName: "terraform"
    condition: and(succeeded(), in(variables['Build.Reason'], 'IndividualCI', 'BatchedCI', 'Manual'))
