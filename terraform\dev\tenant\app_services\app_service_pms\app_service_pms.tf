resource "azurerm_linux_web_app" "app-service-pmss" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant }

  name                = "${var.app_service_pms_prefix}-${each.value.shortname}"
  location            = var.location
  resource_group_name = var.resource_group_name
  service_plan_id     = lookup(var.tenant_app_service_plans, "${each.value.plan}").id
  https_only          = true

  site_config {
    app_command_line = "node apps/iprox.open.pms/server.js"
    application_stack {
      node_version = var.pms_node_version
    }

    ip_restriction_default_action = length(each.value.pmsIpAddresses) != 0 ? "Deny" : "Allow"

    dynamic "ip_restriction" {
      for_each = each.value.pmsIpAddresses
      content {
        ip_address = ip_restriction.value
        name       = ip_restriction.value
        priority   = 100
        action     = "Allow"
      }
    }
  }

  identity {
    type = "SystemAssigned"
  }

  app_settings = merge(var.pms_app_settings, { # use dynammic blocks
    "APPLICATIONINSIGHTS_CONNECTION_STRING" = lookup(var.tenant_application_insights_pmss, "${each.value.name}").connection_string
    "IPROX_OPEN_API_URL"                    = each.value.portalCustomDomain.provider != "adn-awn" ? "https://${each.value.apiCustomDomain.hostName}/api/v1" : "https://${lookup(var.tenant_app_service_apis, "${each.value.name}").name}.azurewebsites.net/api/v1"
    "NEXTAUTH_URL"                          = each.value.portalCustomDomain.provider != "adn-awn" ? "https://${each.value.pmsCustomDomain.hostName}" : "https://${var.app_service_pms_prefix}-${each.value.shortname}.azurewebsites.net"
    "PMS_AUTH_SERVER_PASSWORD"              = var.sql_server_admin_password
    "PMS_NEXT_DATABASE_URL"                 = "sqlserver://${var.sql_server_name}.database.windows.net:1433;database=${lookup(var.tenant_sql_database_pms_auths, "${each.value.name}").name};user=${var.sql_server_admin_username}@${var.sql_server_name};password=${var.sql_server_admin_password};encrypt=true;trustServerCertificate=false;hostNameInCertificate=*.database.windows.net;loginTimeout=30;"
    "IPROX_OPEN_PORTAL_URL"                 = each.value.portalCustomDomain.provider != "adn-awn" ? "https://${each.value.portalCustomDomain.hostName}" : "https://${var.app_service_portal_prefix}-${each.value.shortname}.azurewebsites.net"
    "NEXT_PUBLIC_SERVICE_DESK_URL"          = "https://iprox-open-dev.service-now.com"
    "AZURE_AD_PROVIDERS" = jsonencode([
      for azureAd in each.value.azureAd : {
        "id"           = azureAd.id,
        "name"         = azureAd.name,
        "clientId"     = azureAd.pmsClientId,
        "clientSecret" = lookup(var.tenant_secrets, "${each.value.name}${substr(azureAd.id, 8, -1)}").secret,
        "uri"          = "api://${azureAd.clientId}/",
        "tenantId"     = azureAd.tenantId
      }
    ])
  })

  tags = {
    "${var.tenant_alias_tag_name}"     = "${each.value.name}"
    "${var.tenant_shortname_tag_name}" = "${each.value.shortname}"
    "${var.product_tag_name}"          = "${var.product_name}"
    "${var.env_tag_name}"              = "${var.env}"
  }
}
