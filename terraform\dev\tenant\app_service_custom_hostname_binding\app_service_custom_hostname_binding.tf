// API
resource "azurerm_app_service_custom_hostname_binding" "app-service-api-custom-domain" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant if tenant.apiCustomDomain.provider == "adn-afd" }

  hostname            = each.value.apiCustomDomain.hostName
  app_service_name    = lookup(var.tenant_app_service_apis, "${each.value.name}").name
  resource_group_name = var.resource_group_name
}

resource "azurerm_app_service_managed_certificate" "app-service-api-managed-certificate" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant if tenant.apiCustomDomain.provider == "adn-afd" }

  custom_hostname_binding_id = azurerm_app_service_custom_hostname_binding.app-service-api-custom-domain[each.key].id

  depends_on = [azurerm_app_service_custom_hostname_binding.app-service-api-custom-domain]
}

resource "azurerm_app_service_certificate_binding" "app-service-api-certificate-binding" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant if tenant.apiCustomDomain.provider == "adn-afd" }

  hostname_binding_id = azurerm_app_service_custom_hostname_binding.app-service-api-custom-domain[each.key].id
  certificate_id      = azurerm_app_service_managed_certificate.app-service-api-managed-certificate[each.key].id
  ssl_state           = "SniEnabled"

  depends_on = [azurerm_app_service_custom_hostname_binding.app-service-api-custom-domain,
  azurerm_app_service_managed_certificate.app-service-api-managed-certificate]
}

// PMS
resource "azurerm_app_service_custom_hostname_binding" "app-service-pms-custom-domain" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant if tenant.pmsCustomDomain.provider == "adn-afd" }

  hostname            = each.value.pmsCustomDomain.hostName
  app_service_name    = lookup(var.tenant_app_service_pmss, "${each.value.name}").name
  resource_group_name = var.resource_group_name
}

resource "azurerm_app_service_managed_certificate" "app-service-pms-managed-certificate" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant if tenant.pmsCustomDomain.provider == "adn-afd" }

  custom_hostname_binding_id = azurerm_app_service_custom_hostname_binding.app-service-pms-custom-domain[each.key].id

  depends_on = [azurerm_app_service_custom_hostname_binding.app-service-pms-custom-domain]
}

resource "azurerm_app_service_certificate_binding" "app-service-pms-certificate-binding" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant if tenant.pmsCustomDomain.provider == "adn-afd" }

  hostname_binding_id = azurerm_app_service_custom_hostname_binding.app-service-pms-custom-domain[each.key].id
  certificate_id      = azurerm_app_service_managed_certificate.app-service-pms-managed-certificate[each.key].id
  ssl_state           = "SniEnabled"

  depends_on = [azurerm_app_service_custom_hostname_binding.app-service-pms-custom-domain,
  azurerm_app_service_managed_certificate.app-service-pms-managed-certificate]
}

// PORTAL
resource "azurerm_app_service_custom_hostname_binding" "app-service-portal-custom-domain" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant if tenant.portalCustomDomain.provider == "adn-afd" }

  hostname            = each.value.portalCustomDomain.hostName
  app_service_name    = lookup(var.tenant_app_service_portals, "${each.value.name}").name
  resource_group_name = var.resource_group_name
}

resource "azurerm_app_service_managed_certificate" "app-service-portal-managed-certificate" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant if tenant.portalCustomDomain.provider == "adn-afd" }

  custom_hostname_binding_id = azurerm_app_service_custom_hostname_binding.app-service-portal-custom-domain[each.key].id

  depends_on = [azurerm_app_service_custom_hostname_binding.app-service-portal-custom-domain]
}

resource "azurerm_app_service_certificate_binding" "app-service-portal-certificate-binding" {
  for_each = { for tenant in var.tenants_configuration : tenant.name => tenant if tenant.portalCustomDomain.provider == "adn-afd" }

  hostname_binding_id = azurerm_app_service_custom_hostname_binding.app-service-portal-custom-domain[each.key].id
  certificate_id      = azurerm_app_service_managed_certificate.app-service-portal-managed-certificate[each.key].id
  ssl_state           = "SniEnabled"

  depends_on = [azurerm_app_service_custom_hostname_binding.app-service-portal-custom-domain,
  azurerm_app_service_managed_certificate.app-service-portal-managed-certificate]
}
